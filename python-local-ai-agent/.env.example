# Base URL for the OpenAI compatible instance (default is http://localhost:11434/v1 for ollama)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
LLM_BASE_URL=

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Open Router: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: No need to set this unless you specifically configured an API key
LLM_API_KEY=

# The LLM you want to use for the agents. Make sure this LLM supports tools (especially important if using Ollama)!
# Ollama example: qwen3:14b
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
LLM_CHOICE=

# Supabase configuration - get these values from your .env for local AI
# For the local AI package - this will be:
#    http://localhost:8000 if your agent is running outside of Docker
#    http://kong:8000 if your agent is running in a container in the local-ai network
SUPABASE_URL=
SUPABASE_SERVICE_KEY=

# Set the SearXNG endpoint if using SearXNG for agent web search
# For the local AI package - this will be:
#    http://localhost:8081 if your agent is running outside of Docker
#    http://searxng:8080 if your agent is running in a container in the local-ai network
SEARXNG_BASE_URL=

# Bearer token for your API endpoint
# This is the content that comes after "Bearer "
BEARER_TOKEN=

# Set this if you are running the OpenAI compatible demo and want to test with OpenAI
OPENAI_API_KEY=