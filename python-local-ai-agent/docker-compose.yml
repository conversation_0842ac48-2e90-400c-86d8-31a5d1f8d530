include:
  - ../../../local-ai-packaged/docker-compose.yml

services:
  python-local-ai-agent:
    build: .
    container_name: python-local-ai-agent
    ports:
      - "8055:8055"
    environment:
      - LLM_BASE_URL=${LLM_BASE_URL:-http://ollama:11434/v1}
      - LLM_API_KEY=${LLM_API_KEY:-ollama}
      - LLM_CHOICE=${LLM_CHOICE:-qwen3:14b}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SEARXNG_BASE_URL=http://searxng:8080
      - BEARER_TOKEN=${BEARER_TOKEN}