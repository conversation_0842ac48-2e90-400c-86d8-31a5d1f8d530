{"name": "Open WebUI Agent with Web Search", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId }}"}, "id": "93b4e520-196c-4d47-ae29-5166cac6656e", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-1020, -260], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $('Webhook1').item.json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $('Webhook1').item.json.body.sessionId }}", "type": "string"}]}, "options": {}}, "id": "f413ed88-5706-4c59-8db0-740d346d955a", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1200, -480]}, {"parameters": {"public": true, "options": {}}, "id": "32b4e5d3-baa3-49a1-8778-394e6527f9e8", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1660, -360], "webhookId": "128c7fd7-1f0e-4320-9d91-499cbbe1a39a"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the users asks you to check a document or the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "15da50c9-38a5-4a75-be30-a81ceb151d7e", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-980, -480]}, {"parameters": {"content": "## Web Search Tool", "height": 400, "width": 1680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 220], "id": "1964c10e-edfb-4f61-86c0-448a21aac8eb", "name": "Sticky Note7"}, {"parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1680, 360], "id": "6f2c2692-84b0-457c-9cd8-7a475a8995b0", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-860, -260], "id": "9d26b1bf-0dc2-40e8-b0da-b88f1e2f6609", "name": "Web Search Tool"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1480, 360], "id": "461520d4-370e-481c-8f78-6edd6ea87423", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1280, 360], "id": "b6c1be02-9bc0-45cd-b78d-09dc8b541687", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1080, 360], "id": "1c490ba8-87b7-4edb-90d6-c27c9ba92cdb", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-880, 360], "id": "1e1fabe5-a258-4f08-8394-9f8fba5eb24f", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-280, 360], "id": "36ab3b3e-1e4b-40bb-aa31-425f546c2fe3", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-680, 360], "id": "38d2389b-1368-4c0a-9964-3f4eac930f00", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [-480, 360], "id": "4b0f50d2-1c62-42b3-8877-a306b6aad3f1", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"model": "qwen2.5:14b-instruct-q4_K_M", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [-1180, -260], "id": "4a88e757-3746-4d86-8473-80656434ac97", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-540, -260], "id": "68b1c7ad-2ff9-4cc4-877d-6aa30e87fcfd", "name": "Edit Fields (Set Output Field)"}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook1').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-1100, -100], "id": "4bc2c2b0-ace5-4290-be29-4c73f9d0ed12", "name": "Open WebUI Metadata LLM"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "### Task", "operator": {"type": "string", "operation": "notStartsWith"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1420, -140], "id": "3306140e-db0f-4efa-ae4b-87ab4389f0b6", "name": "Chat message or metadata request?"}, {"parameters": {"options": {}}, "id": "cc8aae54-563e-4786-99c4-eafdd29122a5", "name": "Respond to Webhook1", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-300, -260]}, {"parameters": {"httpMethod": "POST", "path": "invoke-n8n-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "768234d5-081d-4058-b57f-c59017a10441", "name": "Webhook1", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1660, -140], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "mHpyNagWaWiMPLzi", "name": "Open WebUI Auth"}}}, {"parameters": {"model": "qwen2.5:14b-instruct-q4_K_M", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [-1020, 60], "id": "4f98290b-644b-4f26-8979-8b1ddd3006f9", "name": "Ollama Chat Model1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"content": "## Open WebUI Compatible n8n Agent with Web Search (100% Local)", "height": 760, "width": 1680, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, -560], "id": "4498ec28-9135-4707-b701-033e80902328", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "SearXNG", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields (Set Output Field)": {"main": [[{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "Open WebUI Metadata LLM": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "Chat message or metadata request?": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Open WebUI Metadata LLM", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "Chat message or metadata request?", "type": "main", "index": 0}]]}, "Ollama Chat Model1": {"ai_languageModel": [[{"node": "Open WebUI Metadata LLM", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "0183f370-583c-41e0-add0-633c0dd2d272", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "8NGtjxzT5j3cWdbP", "tags": []}