{"name": "YouTube Video Summarizer", "nodes": [{"parameters": {"url": "https://www.googleapis.com/youtube/v3/captions", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "videoId", "value": "={{ $('Execute Workflow Trigger').item.json.query.video_id }}"}, {"name": "part", "value": "snippet"}]}, "options": {}}, "id": "723432ed-d7c9-4c19-961c-5c6edb56f0ec", "name": "Retrieve Caption Data", "type": "n8n-nodes-base.httpRequest", "position": [1260, 400], "typeVersion": 4.2, "credentials": {"youTubeOAuth2Api": {"id": "BdaMM4r3eOkvihTV", "name": "YouTube account Cole"}}}, {"parameters": {"operation": "text", "destinationKey": "content", "options": {}}, "id": "a38a76ef-2b88-400d-801d-18d370f72df8", "name": "Caption File Conversion", "type": "n8n-nodes-base.extractFromFile", "position": [1880, 400], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "eaf7dcb5-91cf-4405-917b-38845f0ef78d", "name": "caption", "type": "object", "value": "={{ $jmespath( $json.items, \"[?snippet.language == 'en'] | [0]\" ) }}"}]}, "options": {}}, "id": "526ab1a6-1bbf-45b8-b3d1-64f2cd102bc3", "name": "Find English Captions", "type": "n8n-nodes-base.set", "position": [1480, 400], "typeVersion": 3.3}, {"parameters": {"url": "=https://www.googleapis.com/youtube/v3/captions/{{ $json.caption.id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "options": {}}, "id": "87fe7be1-f274-4aaf-bf09-cd77f3b720c5", "name": "Download Captions", "type": "n8n-nodes-base.httpRequest", "position": [1680, 400], "typeVersion": 4.2, "credentials": {"youTubeOAuth2Api": {"id": "BdaMM4r3eOkvihTV", "name": "YouTube account Cole"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "b4a4acb2-b2c4-45c1-af2e-02636999af94", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "7e5cd848-8744-4e58-a9c9-912faefd5d76", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1380, -60]}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "218e4eb1-7afd-4d67-bcc3-a665a4651d16", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2020, -60]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Edit Fields').item.json.session_id }}", "tableName": "messages"}, "id": "2b0867f3-6b24-4aac-8381-303bc13bd0c5", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1680, 160], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"options": {}}, "id": "76da72db-d925-4fd2-8416-be6b5351d1c8", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1540, 160], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"name": "fetch_youtube_video_summary_by_id", "description": "Call this tool to get the summary of a YouTube video based on id.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "responsePropertyName": "summary", "fields": {"values": [{"name": "session_id", "stringValue": "={{$('Edit Fields').item.json.session_id}}"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"video_id\": \"pC17ge_2n0Q\"\n}"}, "id": "9f03bc40-4d48-41d3-851a-52aad567dd53", "name": "Call n8n Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1820, 160]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"-> Summarizing YouTube video...\",\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "bb8ba969-2c61-406b-8ac1-f6ed5969dc10", "name": "Add AI Documentation Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1040, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {}, "id": "221df2f3-3109-47ab-a8f6-fc267706ae06", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [840, 400]}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Summarise this transcript into three bullet points to sum up what the video is about and why someone should watch it: {{ $json[\"content\"] }}"}]}, "options": {}}, "id": "0b47e656-fdda-48d2-aff6-e36791a99018", "name": "Caption Summary with ChatGPT", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2100, 400], "typeVersion": 1.3, "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "63b85480-7d13-46d3-9670-e4f3d0092f2e", "name": "summary", "value": "={{ $json.message.content }}", "type": "string"}]}, "options": {}}, "id": "26cab235-a074-4a77-8b24-066b71a29ece", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2480, 400]}, {"parameters": {"promptType": "define", "text": "={{ $('Edit Fields').item.json.query }}", "options": {"systemMessage": "=You are an expert at summarizing YouTube videos based on a video ID and discussing them with users. Whenever you use your tool to summarize a video, you only pass in the ID of the video (like pC17ge_2n0Q). You don't include any other part of the URL."}}, "id": "93661fe6-d1dc-4b71-a8dc-4b74a126a540", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1620, -60]}, {"parameters": {"httpMethod": "POST", "path": "invoke-youtube-summarizer", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "3feef037-1e94-4fef-a8b8-5afa4dbcd518", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1140, -60], "webhookId": "ba5e9235-01af-47f8-9f72-29df862bfa0f", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "# YouTube Video Summarizer Agent\n\nAuthor: [<PERSON>](https://n8n.io/creators/mikerussell/)\n\nThis n8n-powered agent is a conversational AI assistant that creates comprehensive summaries of YouTube videos from <PERSON>'s channel. It can process both video IDs and full YouTube links, providing detailed summaries and engaging in follow-up discussions about the video content.\n\n## Features\n\n- Processes YouTube video IDs and full URLs\n- Retrieves and analyzes video captions\n- Generates detailed video summaries\n- Supports conversational follow-up questions\n- Maintains context for natural dialogue\n- Extracts key points and insights", "height": 441.**************, "width": 651.*************, "color": 6}, "id": "47828cd8-97a6-4fa3-906c-8c8029c93fd8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [380, -140]}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.chayahfitness.com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer 7vnvV*ELn.#FWNV(87g2WQc", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.chayahfitness.com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "https://www.youtube.com/watch?v=pC17ge_2n0Q", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.chayahfitness.com/webhook-test/invoke-n8n-expert", "executionMode": "test"}}]}, "connections": {"Find English Captions": {"main": [[{"node": "Download Captions", "type": "main", "index": 0}]]}, "Retrieve Caption Data": {"main": [[{"node": "Find English Captions", "type": "main", "index": 0}]]}, "Caption File Conversion": {"main": [[{"node": "Caption Summary with ChatGPT", "type": "main", "index": 0}]]}, "Download Captions": {"main": [[{"node": "Caption File Conversion", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Add AI Documentation Message to DB": {"main": [[{"node": "Retrieve Caption Data", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Add AI Documentation Message to DB", "type": "main", "index": 0}]]}, "Caption Summary with ChatGPT": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "358abfb0-5673-4265-874d-b40450085964", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "pbIvEAU2iVZ0IoVi", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}