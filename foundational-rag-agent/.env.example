# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-mini  # Or other OpenAI model

# Supabase configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

# Embedding configuration
EMBEDDING_MODEL=text-embedding-3-small  # OpenAI embedding model

# Application settings
CHUNK_SIZE=1000  # Size of text chunks for embedding
CHUNK_OVERLAP=200  # Overlap between chunks
MAX_RESULTS=5  # Maximum number of results to return from vector search
