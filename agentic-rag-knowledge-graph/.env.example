# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/agentic_rag
# Example: postgresql://raguser:ragpass123@localhost:5432/agentic_rag_db

# Neo4j Configuration for Knowledge Graph
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# LLM Provider Configuration
# Set this to either openai, openrouter, ollama, or gemini
LLM_PROVIDER=openai

# Base URL for the OpenAI compatible instance (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
# Gemini: https://generativelanguage.googleapis.com/v1beta
LLM_BASE_URL=https://api.openai.com/v1

# API Key for LLM provider
# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# OpenRouter: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: No need to set this unless you specifically configured an API key
# Gemini: Get your API key from Google AI Studio
LLM_API_KEY=sk-your-api-key-here

# The LLM you want to use for the agents. Make sure this LLM supports tools!
# OpenAI example: gpt-4.1-mini
# OpenRouter example: anthropic/claude-3-5-sonnet
# Ollama example: qwen2.5:14b-instruct
# Gemini example: gemini-2.5-flash
LLM_CHOICE=gpt-4.1-mini

# Embedding Provider Configuration
# Set this to either openai or ollama (openrouter/gemini don't have embedding models)
EMBEDDING_PROVIDER=openai

# Base URL for embedding models
# OpenAI: https://api.openai.com/v1
# Ollama: http://localhost:11434/v1
EMBEDDING_BASE_URL=https://api.openai.com/v1

# API Key for embedding provider
EMBEDDING_API_KEY=sk-your-api-key-here

# The embedding model you want to use for RAG
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL=text-embedding-3-small

# Ingestion-specific LLM (can be different/faster model for processing)
# Leave empty to use the same as LLM_CHOICE
INGESTION_LLM_CHOICE=gpt-4.1-nano

# Application Configuration
APP_ENV=development
LOG_LEVEL=INFO
APP_HOST=0.0.0.0
APP_PORT=8058

# Chunking Configuration (optimized for Graphiti token limits)
CHUNK_SIZE=800
CHUNK_OVERLAP=150
MAX_CHUNK_SIZE=1500

# Vector Search Configuration
VECTOR_DIMENSION=1536  # For OpenAI text-embedding-3-small
MAX_SEARCH_RESULTS=10

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
MAX_MESSAGES_PER_SESSION=100

# Rate Limiting
RATE_LIMIT_REQUESTS=60
RATE_LIMIT_WINDOW_SECONDS=60

# File Processing
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_EXTENSIONS=.md,.txt

# Debug Configuration
DEBUG_MODE=false
ENABLE_PROFILING=false