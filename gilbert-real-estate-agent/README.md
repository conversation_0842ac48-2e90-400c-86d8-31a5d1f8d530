# Gilbert: AI-powered Real Estate Assistant

Author: [<PERSON>](https://stellular-halva-641b23.netlify.app/)

**Platform:** Voiceflow (you can import the .vf file into your own Voiceflow to check out the flow)

**Note:** All API keys have been removed from the .vf file

<PERSON> is an AI-powered assistant designed to streamline real estate services for <PERSON>, a fictional real estate agent operating in the United States. With access to advanced tools and integrations, <PERSON> simplifies the process of finding properties for renting or buying by providing instant assistance to potential clients.

Please read [Documentation](https://ivans-organization-61.gitbook.io/gilbert-ai-docs) for a better experience.

## Features

- Tailored for Real Estate: Specifically supports <PERSON>’s real estate services in the US
- Powered by Zillow: Searches properties nationwide based on user preferences
- Intelligent Conversations: Answers real estate questions while maintaining context
- Seamless Location Assistance: Utilizes Google Maps to clarify locations and nearby amenities
- Consultation Scheduling: Books consultations with <PERSON> by checking availability via Google Calendar
- User-Friendly Interaction: Automates property searches, answers queries, and connects clients for further assistance
- Built with Voiceflow: Compatible with websites, AI agent hubs like Live Agent Studio, and integration frameworks like Voiceflousion

## How It Works

1. Gathers user preferences and location requirements
2. Searches Zillow for matching rental or purchase listings
3. Integrates Google Maps to provide local amenity details
4. Facilitates booking of consultations by syncing with <PERSON>’s Google Calendar
5. Provides continuous support for property-related queries and scheduling

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.