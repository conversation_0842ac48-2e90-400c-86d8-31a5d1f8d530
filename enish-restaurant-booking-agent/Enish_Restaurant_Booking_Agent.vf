{"version": {"_version": 12, "autoSaveFromRestore": false, "canvasTemplates": [{"id": "sg9zy34i0", "name": "Basic Conversation Path", "color": "#5b9fd7", "nodeIDs": ["643872d482cf9d2273b905df", "643872d482cf9d2273b905e1", "643872d482cf9d2273b905e3"]}, {"id": "5bgq3341p", "name": "Save Information", "color": "#000000", "nodeIDs": ["6438784f82cf9d2273b90dd4", "6438784f82cf9d2273b90dd6", "6438784f82cf9d2273b90dd8", "6438784f82cf9d2273b90dda"]}, {"id": "byav33785", "name": "API Call Example", "color": null, "nodeIDs": ["645d718f83103cca7a3cecc1", "645d718f83103cca7a3cecc3", "645d718f83103cca7a3cecc5", "645d718f83103cca7a3cecc7"]}], "components": [], "creatorID": 1464246, "manualSave": false, "name": "Initial Version", "platformData": {"slots": [], "intents": [{"key": "6579c6bf984565862f64b783", "name": "Yes", "slots": [], "inputs": [{"text": "sure", "slots": []}, {"text": "certainly", "slots": []}, {"text": "affirmative", "slots": []}, {"text": "okay", "slots": []}, {"text": "ok", "slots": []}, {"text": "ya", "slots": []}, {"text": "yep", "slots": []}, {"text": "yup", "slots": []}, {"text": "yea", "slots": []}, {"text": "yes", "slots": []}], "noteID": "8o7i20pvk", "description": "Trigger this intent when the user responds affirmatively or agrees to a question or statement using words like \"yes\", \"yea\", \"yup\", \"yep\", \"ya\", \"ok\", \"okay\", \"affirmative\", \"certainly\", or \"sure\"."}, {"key": "6579c778dd3cca0fe0e0b152", "name": "No", "slots": [], "inputs": [{"text": "negative", "slots": []}, {"text": "nevermind", "slots": []}, {"text": "not", "slots": []}, {"text": "no thanks", "slots": []}, {"text": "no way", "slots": []}, {"text": "not now", "slots": []}, {"text": "nah", "slots": []}, {"text": "nay", "slots": []}, {"text": "nope", "slots": []}, {"text": "no", "slots": []}], "noteID": "8o7i30pnf", "description": "Trigger this intent when the user responds negatively or declines something, using words like \"no\", \"nope\", \"nay\", \"nah\", \"not now\", \"no way\", \"no thanks\", \"not\", \"nevermind\", or \"negative\"."}, {"key": "None", "name": "None", "slots": [], "inputs": [], "noteID": null}], "settings": {"restart": true, "repeat": 100, "locales": ["en-US"], "defaultVoice": "Alexa"}, "publishing": {"avatar": "https://cdn.voiceflow.com/assets/logomark.png", "color": "#DD2EF1", "description": "Our virtual agent is here to help you.", "feedback": false, "image": "https://cdn.voiceflow.com/assets/logomark.png", "persistence": "localStorage", "position": "right", "spacing": {"side": 24, "bottom": 24}, "title": "Enish Table Reservation Assistant", "watermark": true}, "platform": "webchat"}, "programResources": {"messages": {"678fc5923a5bd540470e71eb": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "The provided OTP has expired, would you like to recieve another OTP?"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488552": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Second Option"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488555": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "How can I help you?"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488558": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "First Option"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c9000748855b": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "What is your name?"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c9000748855e": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Hello "}, {"text": "{name}"}, {"text": " !"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488561": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Twitter is the best 🐦"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488564": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Error"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c90007488567": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "This flow captures a users question and sends it to the OpenAI API"}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c9000748856a": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": ""}, {"text": "{response}"}, {"text": " "}]}], "delay": null}, "condition": null}]}}, "66c379f1d907c9000748856d": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Hello Welcome to <PERSON><PERSON>, how may I be of help today?"}]}], "delay": null}, "condition": null}]}}, "66c59dd76f3f9b24e0b43c70": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Oh great, kindly select your party size."}]}], "delay": null}, "condition": null}]}}, "66c5a1d26f3f9b24e0b43ecc": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Kindly select a time that works for you."}]}], "delay": null}, "condition": null}]}}, "66c5a4de6f3f9b24e0b440e0": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Kindly note that this booking needs a deposit of "}, {"text": "{deposit_fee}"}, {"text": " Pounds."}]}], "delay": null}, "condition": null}]}}, "66c5a7806f3f9b24e0b44358": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Do you agree to these terms and would like to proceed with payment?"}]}], "delay": null}, "condition": null}]}}, "66c5a7be6f3f9b24e0b443aa": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Hi welcome back "}, {"text": "{first_name}"}, {"text": ", type \"Done\" to confirm you have made the payment so I can proceed to confirm your reservation."}]}, {"children": [{"children": [{"text": ""}]}]}], "delay": null}, "condition": null}]}}, "66c5a83d6f3f9b24e0b44477": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Kind note that your reservation would not be booked without any deposit made."}]}], "delay": null}, "condition": null}]}}, "66c5a8e86f3f9b24e0b44503": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "would you like to continue with the payment?"}]}], "delay": null}, "condition": null}]}}, "66c5a92d6f3f9b24e0b44565": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Alright thanks for checking out our service. Have a nice day."}]}], "delay": null}, "condition": null}]}}, "66c5f77527b27f078d3e7137": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Ooops, sorry your payment was not completed. Kindly try again."}]}], "delay": null}, "condition": null}]}}, "66c5f9e927b27f078d3e7243": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Payment process has been canceled"}]}], "delay": null}, "condition": null}]}}, "66cf35f093364975386e4223": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Oh great, kindly note that you are required to visit this chat again after you are done with your payment for confirmation purpose."}]}], "delay": null}, "condition": null}]}}, "66cf365893364975386e425f": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "After making payment you will be redirected back here. Please type \"Done\" when you are back on this page."}]}], "delay": null}, "condition": null}]}}, "66da131c5e1bae3341b56507": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Please select \"Make Payment\" to be directed to the payment page.  \r"}]}], "delay": null}, "condition": null}]}}, "66da14195e1bae3341b56583": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Thank you for your reservation. We look forward to welcoming you at our restaurant."}]}], "delay": null}, "condition": null}]}}, "66da148d5e1bae3341b565db": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Your payment has been successfully confirmed and your reservation has been made."}]}, {"children": [{"text": "A booking confirmation has been sent to your email with your reservation details."}]}], "delay": null}, "condition": null}]}}, "66da14fe5e1bae3341b56624": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Kindly select one of our outlets."}]}], "delay": null}, "condition": null}]}}, "66d2ebf67f0b84ecf04fc94a": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "These are the available dates. Kindly select a date or \"Show More\" to view more dates."}]}], "delay": null}, "condition": null}]}}, "66d5cd12f9fabfe28b707605": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Alright "}, {"text": "{first_name}"}, {"text": ", may I have your last your last name please."}]}], "delay": null}, "condition": null}]}}, "66d5ccc17f0b84ecf05110ea": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Please provide your email address so we can send you a confirmation of your reservation."}]}], "delay": null}, "condition": null}]}}, "66d5cde7f9fabfe28b707623": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Alright may I have your first name please."}]}], "delay": null}, "condition": null}]}}, "66d5ce40f9fabfe28b707627": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Thank you "}, {"text": "{first_name}"}, {"text": ", and your mobile number please."}]}], "delay": null}, "condition": null}]}}, "66db0378d8e20c0b54aaec9b": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Kindly let me know if you have any special request."}]}, {"children": [{"text": "(For allergies, intolerances and dietary requirements, please speak to the restaurant directly.)"}]}], "delay": null}, "condition": null}]}}, "678da96a4e984c4ee9b29b4c": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Sorry to hear you want to cancel your reservation."}]}, {"children": [{"text": "No worries, you can always book next time."}]}, {"children": [{"text": "Kindly provide your booking code to help furhter."}]}], "delay": null}, "condition": null}]}}, "678dabd24e984c4ee9b29baa": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Thank you "}, {"text": "{first_name}"}, {"text": " for providing your booking code. "}]}, {"children": [{"text": "I have your table reservation details as follows:"}]}, {"children": [{"text": "1. Full name: "}, {"text": "{first_name}"}, {"text": " "}, {"text": "{last_name}"}, {"text": " "}]}, {"children": [{"text": "2. Email address: "}, {"text": "{email_address}"}, {"text": " "}]}, {"children": [{"text": "3. Reservation Date: "}, {"text": "{reservation_date}"}, {"text": " "}]}, {"children": [{"text": "4. <PERSON><PERSON>: "}, {"text": "{restaurant_location}"}, {"text": " "}]}, {"children": [{"text": "5. Party size: "}, {"text": "{party_size}"}, {"text": " "}]}, {"children": [{"children": [{"text": ""}]}]}, {"children": [{"text": "Is this the reservation you would like to cancel?"}]}, {"children": [{"children": [{"text": ""}]}]}, {"children": [{"children": [{"text": ""}]}]}], "delay": null}, "condition": null}]}}, "678daccd4e984c4ee9b29bca": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Unfortunately, we could not find any reservation made with the booking code you provided. "}]}, {"children": [{"text": "Please contact our support for assistance."}]}], "delay": null}, "condition": null}]}}, "678dad624e984c4ee9b29be4": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Alright, that is duly noted. We have sent a one-time password (OTP) to your email. Please enter it below to complete your booking cancellation. "}]}], "delay": null}, "condition": null}]}}, "678dadd54e984c4ee9b29bf9": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Please contact our support assistance."}]}, {"children": [{"text": "Have a nice day."}]}], "delay": null}, "condition": null}]}}, "678db1b34e984c4ee9b29d7e": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Unfortunately, we could not proceed with your request at this time. Kindly contact our support for assistance."}]}], "delay": null}, "condition": null}]}}, "678db2304e984c4ee9b29e0d": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "The provided OTP is invalid, would you like to receive another OTP?"}]}], "delay": null}, "condition": null}]}}, "678db2c24e984c4ee9b29ec0": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Your booked reservation for "}, {"text": "{reservation_date}"}, {"text": " at "}, {"text": "{restaurant_location}"}, {"text": " has been successfully cancelled. An email has been sent you on the next steps to get your refund."}]}], "delay": null}, "condition": null}]}}}, "prompts": {}}, "prototype": {"type": "chat", "data": {"name": "<PERSON>ish <PERSON>", "locales": ["en-US"]}, "model": {"intents": [], "slots": []}, "context": {"stack": [{"programID": "64386e21bb106b044ea34b78", "storage": {}, "variables": {}, "diagramID": "64386e21bb106b044ea34b78"}], "variables": {}}, "surveyorContext": {"nonDraftResponsesMap": {}, "responseMessagesByDiscriminatorIDMap": {}, "responseDiscriminatorsByResponseIDMap": {}, "functionDefinitions": {}, "referencedResponseIDs": ["66c379f1d907c90007488552", "66c379f1d907c90007488555", "66c379f1d907c90007488558", "66c379f1d907c9000748855b", "66c379f1d907c9000748855e", "66c379f1d907c90007488561", "66c379f1d907c90007488564", "66c379f1d907c90007488567", "66c379f1d907c9000748856a", "66c379f1d907c9000748856d", "66c59dd76f3f9b24e0b43c70", "66c5a1d26f3f9b24e0b43ecc", "66c5a4de6f3f9b24e0b440e0", "66c5a7806f3f9b24e0b44358", "66c5a7be6f3f9b24e0b443aa", "66c5a83d6f3f9b24e0b44477", "66c5a8e86f3f9b24e0b44503", "66c5a92d6f3f9b24e0b44565", "66c5f77527b27f078d3e7137", "66c5f9e927b27f078d3e7243", "66cf35f093364975386e4223", "66cf365893364975386e425f", "66d2ebf67f0b84ecf04fc94a", "66d5ccc17f0b84ecf05110ea", "66d5cd12f9fabfe28b707605", "66d5cde7f9fabfe28b707623", "66d5ce40f9fabfe28b707627", "66da131c5e1bae3341b56507", "66da14195e1bae3341b56583", "66da148d5e1bae3341b565db", "66da14fe5e1bae3341b56624", "66db0378d8e20c0b54aaec9b", "678da96a4e984c4ee9b29b4c", "678dabd24e984c4ee9b29baa", "678daccd4e984c4ee9b29bca", "678dad624e984c4ee9b29be4", "678dadd54e984c4ee9b29bf9", "678db1b34e984c4ee9b29d7e", "678db2304e984c4ee9b29e0d", "678db2c24e984c4ee9b29ec0", "678fc5923a5bd540470e71eb"], "slotsMap": {}, "platform": "webchat", "products": {}, "extraSlots": [], "interfaces": [], "permissions": [], "projectType": "chat", "extraIntents": [], "usedEventsSet": [], "usedIntentsSet": [], "goToIntentsSet": [], "entitiesMap": {}, "variableMap": {}, "intentsMap": {}, "requiredEntitiesMap": {}, "eventsMap": {}, "cmsVariables": {"url": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "sessions": {"isSystem": true, "description": "The number of times a particular user has opened the app.", "defaultValue": null}, "user_id": {"isSystem": true, "description": "The user's unique ID.", "defaultValue": null}, "timestamp": {"isSystem": true, "description": "UNIX timestamp (number of seconds since January 1st, 1970 at UTC).", "defaultValue": null}, "platform": {"isSystem": true, "description": "The platform your agent is running on (e.g. \"voiceflow\").", "defaultValue": null}, "intent_confidence": {"isSystem": true, "description": "The confidence interval (measured as a value from 0 to 100) for the most recently matched intent.", "defaultValue": null}, "last_response": {"isSystem": true, "description": "The agent's last response (text/speak) in a string.", "defaultValue": null}, "last_event": {"isSystem": true, "description": "The object containing the last event that the user client has triggered.", "defaultValue": null}, "last_utterance": {"isSystem": true, "description": "The user's last utterance in a text string.", "defaultValue": null}, "vf_memory": {"isSystem": true, "description": "Last 10 user inputs and agent responses in a string (e.g. \"agent: How can i help?\"\nuser: What's the weather today?).", "defaultValue": null}, "firstDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "secondDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "thirdDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "fourthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "fifthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "sixthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "seventhDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "eighthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "ninthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "tenthDate": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "showMore": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "stripe_session_id": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "vf_payment_status": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This variable holds the value of the stripe payment", "defaultValue": null}, "party_size": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This variable holds the value of the party size.", "defaultValue": null}, "deposit_fee": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the fee to be deposited\n", "defaultValue": null}, "amount": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the amount to be used in creating payment session from the API.", "defaultValue": null}, "outlet": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the Enish outlets", "defaultValue": null}, "first_name": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user first name\n", "defaultValue": null}, "last_name": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user last name\n", "defaultValue": null}, "email_address": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user's email address", "defaultValue": null}, "mobile_number": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user's mobile number\n", "defaultValue": null}, "special_request": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This variable represents any special request the customer has.", "defaultValue": null}, "restaurant_location": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "reservation_date": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "number_of_guests": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "booking_code": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "provided_otp": {"isArray": false, "isSystem": false, "datatype": "any", "description": "This is the OTP provided by the user", "defaultValue": null}, "otp": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "otp_timestamp": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "current_timestamp": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "otp_timestamp_epoch": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "elapsed_timestamp": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}}, "cmsEntities": {}}, "settings": {}, "platform": "webchat"}, "topics": [{"type": "DIAGRAM", "sourceID": "64386e21bb106b044ea34b78"}], "variables": ["url", "sessions", "user_id", "timestamp", "platform", "intent_confidence", "last_response", "last_event", "last_utterance", "vf_memory", "firstDate", "secondDate", "thirdDate", "fourthDate", "fifthDate", "sixthDate", "seventhDate", "eighthDate", "ninthDate", "tenthDate", "showMore", "stripe_session_id", "vf_payment_status", "party_size", "deposit_fee", "amount", "outlet", "first_name", "last_name", "email_address", "mobile_number", "special_request", "restaurant_location", "reservation_date", "number_of_guests", "booking_code", "provided_otp", "otp", "otp_timestamp", "current_timestamp", "otp_timestamp_epoch", "elapsed_timestamp"], "_id": "678bb820c4618d115e6b0b98", "updatedAt": "2025-01-21T16:06:14.280Z", "publishedAt": "2024-09-06T15:11:24.874Z", "domains": [{"id": "cla06iyr900b206pkh8d4ap8n", "name": "Home", "live": true, "topicIDs": ["64386e21bb106b044ea34b78"], "rootDiagramID": "64386e21bb106b044ea34b78", "updatedBy": 1464246, "updatedAt": "2025-01-18T14:18:08.544Z"}], "projectID": "678bb820c4618d115e6b0b97", "rootDiagramID": "64386e21bb106b044ea34b78", "templateDiagramID": "643872531e80120007759e05"}, "diagrams": {"643872531e80120007759e05": {"name": "Template <PERSON>agram", "type": "TEMPLATE", "zoom": 100, "nodes": {"643872d482cf9d2273b905df": {"type": "block", "data": {"name": "Option 2", "color": "", "steps": ["643872d482cf9d2273b905e5"]}, "nodeID": "643872d482cf9d2273b905df", "coords": [-432.02299332274276, -334.73425308247994]}, "643872d482cf9d2273b905e5": {"type": "message", "data": {"messageID": "66c379f1d907c90007488552", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "643872d482cf9d2273b905e6"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "643872d482cf9d2273b905e5"}, "643872d482cf9d2273b905e1": {"type": "block", "data": {"name": "Start Block", "color": "#5b9fd7", "steps": ["643872d482cf9d2273b905e8", "643872d482cf9d2273b905eb"]}, "nodeID": "643872d482cf9d2273b905e1", "coords": [-845.4801744371416, -539.5792694267975]}, "643872d482cf9d2273b905e8": {"type": "message", "data": {"messageID": "66c379f1d907c90007488555", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "643872d482cf9d2273b905eb", "id": "643872d482cf9d2273b905e9"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "643872d482cf9d2273b905e8"}, "643872d482cf9d2273b905eb": {"type": "buttons", "data": {"name": "Buttons", "noMatch": null, "buttons": [{"id": "xr9vd34tj", "name": "Path 1", "actions": []}, {"id": "cl9vs34c7", "name": "Path 2", "actions": []}], "noReply": null, "intentScope": "GLOBAL", "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "643872d482cf9d2273b905ec"}}, "dynamic": [{"type": "", "target": "643872d482cf9d2273b905e3", "id": "643872d482cf9d2273b905ed", "data": {"points": [{"point": [-679.4801667002234, -399.32923837159325], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-637.4918741411055, -399.32923837159325], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-637.4918741411055, -542.9755028741506], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-595.5035815819875, -542.9755028741506], "toTop": false, "locked": false, "reversed": false, "allowedToTop": true}]}}, {"type": "", "target": "643872d482cf9d2273b905df", "id": "643872d482cf9d2273b905ee", "data": {"points": [{"point": [-679.4801667002234, -344.82921901334083], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-638.2515780028617, -344.82921901334083], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-638.2515780028617, -307.73401942975823], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-597.0229893055001, -307.73401942975823], "toTop": false, "locked": false, "reversed": false, "allowedToTop": true}]}}]}}, "nodeID": "643872d482cf9d2273b905eb"}, "643872d482cf9d2273b905e3": {"type": "block", "data": {"name": "Option 1", "color": "", "steps": ["643872d482cf9d2273b905f0"]}, "nodeID": "643872d482cf9d2273b905e3", "coords": [-430.5038187460724, -569.9756266907931]}, "643872d482cf9d2273b905f0": {"type": "message", "data": {"messageID": "66c379f1d907c90007488558", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "643872d482cf9d2273b905f1"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "643872d482cf9d2273b905f0"}, "6438784f82cf9d2273b90dd4": {"type": "block", "data": {"name": "Use Logic to Route", "color": "#dc8879", "steps": ["6438784f82cf9d2273b90ddc"]}, "nodeID": "6438784f82cf9d2273b90dd4", "coords": [-1964.4846386537488, -626.9744457114995]}, "6438784f82cf9d2273b90ddc": {"type": "ifV2", "data": {"name": "If", "expressions": [{"type": null, "name": "If name is elon musk", "value": [{"logicInterface": "variable", "type": "equals", "value": [{"type": "variable", "value": "name"}, {"type": "value", "value": "elon musk"}]}]}], "noMatch": {"type": "path", "pathName": "All other names"}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": "6438784f82cf9d2273b90dd8", "id": "6438784f82cf9d2273b90ddd", "data": {"points": [{"point": [-1798.484505972181, -492.72447921623757], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1763.5243607307511, -492.72447921623757], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1763.5243607307511, -440.2937029472216], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1728.5642154893217, -440.2937029472216], "toTop": false, "locked": false, "reversed": false, "allowedToTop": true}]}}}, "dynamic": [{"type": "", "target": "6438784f82cf9d2273b90dda", "id": "6438784f82cf9d2273b90dde", "data": {"points": [{"point": [-1798.484505972181, -547.2244999131336], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1764.1493300508728, -547.2244999131336], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1764.1493300508728, -599.0438504799286], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-1729.814154129565, -599.0438504799286], "toTop": false, "locked": false, "reversed": false, "allowedToTop": true}], "event": {"type": "If name is elon musk"}}}]}}, "nodeID": "6438784f82cf9d2273b90ddc"}, "6438784f82cf9d2273b90dd6": {"type": "block", "data": {"name": "Capture Information", "color": "#dc8879", "steps": ["6438784f82cf9d2273b90de0", "6438784f82cf9d2273b90de3"]}, "nodeID": "6438784f82cf9d2273b90dd6", "coords": [-2376.095116200327, -628.3840706346044]}, "6438784f82cf9d2273b90de0": {"type": "message", "data": {"messageID": "66c379f1d907c9000748855b", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "6438784f82cf9d2273b90de3", "id": "6438784f82cf9d2273b90de1"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "6438784f82cf9d2273b90de0"}, "6438784f82cf9d2273b90de3": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "name"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "6438784f82cf9d2273b90dd4", "id": "6438784f82cf9d2273b90de4", "data": {"points": [{"point": [-2210.0950799471193, -488.1341077200336], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-2169.789872823509, -488.1341077200336], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-2169.789872823509, -599.9743336839742], "toTop": false, "locked": false, "reversed": false, "allowedToTop": false}, {"point": [-2129.484665699899, -599.9743336839742], "toTop": false, "locked": false, "reversed": false, "allowedToTop": true}]}}, "else": {"type": "else", "target": null, "id": "6438784f82cf9d2273b90de5"}}, "dynamic": []}}, "nodeID": "6438784f82cf9d2273b90de3"}, "6438784f82cf9d2273b90dd8": {"type": "block", "data": {"name": "Other names", "color": "#dc8879", "steps": ["6438784f82cf9d2273b90de7"]}, "nodeID": "6438784f82cf9d2273b90dd8", "coords": [-1563.5642881833433, -467.2936955577087]}, "6438784f82cf9d2273b90de7": {"type": "message", "data": {"messageID": "66c379f1d907c9000748855e", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "6438784f82cf9d2273b90de8"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "6438784f82cf9d2273b90de7"}, "6438784f82cf9d2273b90dda": {"type": "block", "data": {"name": "<PERSON><PERSON>", "color": "#dc8879", "steps": ["6438784f82cf9d2273b90dea"]}, "nodeID": "6438784f82cf9d2273b90dda", "coords": [-1564.8142881833433, -626.0436955577089]}, "6438784f82cf9d2273b90dea": {"type": "message", "data": {"messageID": "66c379f1d907c90007488561", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "6438784f82cf9d2273b90deb"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "6438784f82cf9d2273b90dea"}, "645d718f83103cca7a3cecc1": {"type": "block", "data": {"name": "Fail", "color": "#5b9fd7", "steps": ["645d718f83103cca7a3cecc9"]}, "nodeID": "645d718f83103cca7a3cecc1", "coords": [-943.7641060825457, -726.7018427371622]}, "645d718f83103cca7a3cecc9": {"type": "message", "data": {"messageID": "66c379f1d907c90007488564", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "645d718f83103cca7a3cecca"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "645d718f83103cca7a3cecc9"}, "645d718f83103cca7a3cecc3": {"type": "block", "data": {"name": "API Call", "color": "#5b9fd7", "steps": ["645d718f83103cca7a3ceccc"]}, "nodeID": "645d718f83103cca7a3cecc3", "coords": [-1337.282813902139, -902.1537022731807]}, "645d718f83103cca7a3ceccc": {"type": "api", "data": {"name": "", "url": "https://api.openai.com/v1/completions", "body": [{"key": "", "val": ""}], "params": [], "method": "POST", "headers": [{"key": "Content-Type", "val": "application/json"}, {"key": "Authorization", "val": "Bearer <ADD API KEY HERE>"}], "content": "{\n  \"model\": \"text-davinci-003\",\n  \"prompt\": \"{last_utterance}\",\n  \"max_tokens\": 7,\n  \"temperature\": 0,\n  \"top_p\": 1,\n  \"n\": 1,\n  \"stream\": false,\n  \"logprobs\": null,\n  \"stop\": \"\\n\"\n}", "mappings": [{"path": "response", "var": "response"}], "bodyType": "rawInput", "selectedAction": "Make a POST Request", "selectedIntegration": "Custom API", "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "645d718f83103cca7a3cecd7", "id": "645d718f83103cca7a3cecce", "data": {"points": [{"point": [-1171.2838307947716, -748.4052156062919]}, {"point": [-1141.0638307947715, -748.4052156062919]}, {"point": [-1141.0638307947715, -821.3652156062917]}, {"point": [-1110.8438307947717, -821.3652156062917], "allowedToTop": false}]}}, "fail": {"type": "fail", "target": "645d718f83103cca7a3cecc9", "id": "645d718f83103cca7a3ceccd", "data": {"points": [{"point": [-1171.2838307947716, -693.9052156062919]}, {"point": [-1140.5238307947716, -693.9052156062919]}, {"point": [-1140.5238307947716, -647.2052156062919]}, {"point": [-1109.7638307947714, -647.2052156062919], "allowedToTop": false}]}}}, "dynamic": []}}, "nodeID": "645d718f83103cca7a3ceccc"}, "645d718f83103cca7a3cecc5": {"type": "block", "data": {"name": "Welcome", "color": "#5b9fd7", "steps": ["645d718f83103cca7a3cecd0", "645d718f83103cca7a3cecd3"]}, "nodeID": "645d718f83103cca7a3cecc5", "coords": [-1722.1832079861151, -907.6478276212674]}, "645d718f83103cca7a3cecd0": {"type": "message", "data": {"messageID": "66c379f1d907c90007488567", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "645d718f83103cca7a3cecd3", "id": "645d718f83103cca7a3cecd1"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "645d718f83103cca7a3cecd0"}, "645d718f83103cca7a3cecd3": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "645d718f83103cca7a3cecc3", "id": "645d718f83103cca7a3cecd4", "data": {"points": [{"point": [-1556.1838307947714, -733.6452156062919]}, {"point": [-1529.2338307947716, -733.6452156062919]}, {"point": [-1529.2338307947716, -875.1552156062919]}, {"point": [-1502.2838307947716, -875.1552156062919], "allowedToTop": true}]}}, "else": {"type": "else", "target": null, "id": "645d718f83103cca7a3cecd5"}}, "dynamic": []}}, "nodeID": "645d718f83103cca7a3cecd3"}, "645d718f83103cca7a3cecc7": {"type": "block", "data": {"name": "Success", "color": "#5b9fd7", "steps": ["645d718f83103cca7a3cecd7"]}, "nodeID": "645d718f83103cca7a3cecc7", "coords": [-944.844860096504, -900.8660415973322]}, "645d718f83103cca7a3cecd7": {"type": "message", "data": {"messageID": "66c379f1d907c9000748856a", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "645d718f83103cca7a3cecd8"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "645d718f83103cca7a3cecd7"}}, "offsetX": 0, "offsetY": 0, "modified": 1681420883, "creatorID": 1464246, "variables": [], "menuItems": [], "_id": "678bb820c4618d115e6b0b9b", "diagramID": "643872531e80120007759e05", "versionID": "678bb820c4618d115e6b0b98"}, "64386e21bb106b044ea34b78": {"name": "ROOT", "type": "TOPIC", "zoom": 100, "nodes": {"start00000000000000000000": {"type": "start", "data": {"name": "Start", "color": "#56b365", "steps": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66b92b2cd1b9fb2b7de56f74", "id": "6032afcf359e8c14c06c0319", "data": {"points": [{"point": [-1287.56, -881.25]}, {"point": [-1186, -881.25]}, {"point": [-1186, -1164.77]}, {"point": [-1084.43, -1164.77], "allowedToTop": true}]}}}, "dynamic": []}, "label": "Start"}, "nodeID": "start00000000000000000000", "coords": [-1352.1943813450835, -903.2435275499747]}, "66b92b2cd1b9fb2b7de56f72": {"type": "message", "data": {"messageID": "66c379f1d907c9000748856d", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66b92b2cd1b9fb2b7de56f73"}}, "builtIn": {}, "dynamic": []}, "name": "Text"}, "nodeID": "66b92b2cd1b9fb2b7de56f72"}, "66b92b2cd1b9fb2b7de56f74": {"type": "block", "data": {"name": "Introduction", "steps": ["66b92b2cd1b9fb2b7de56f72", "66c59d7a2825ec011ad62380"], "color": ""}, "nodeID": "66b92b2cd1b9fb2b7de56f74", "coords": [-919.4279726399093, -1191.7653494362578]}, "66c59d7a2825ec011ad62380": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "jpzq3c04", "name": "Reserve A Table", "actions": []}, {"id": "3f1083coe", "name": "Cancel Reservation", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c59d7a2825ec011ad62382"}}, "dynamic": [{"type": "", "target": "66da14e144e67d133b44d993", "id": "66c59d7a2825ec011ad62381", "data": {"points": [{"point": [-752.54, -1035.45]}, {"point": [-717.17, -1035.45]}, {"point": [-717.17, -1220.31]}, {"point": [-681.8, -1220.31], "allowedToTop": false}]}}, {"id": "66c59d892825ec011ad62385", "type": "", "target": "678da963c2e29027e4e5ed1e", "data": {"points": [{"point": [-1085.61, -978.66], "reversed": true}, {"point": [-1109.61, -978.66]}, {"point": [-1109.61, -890.2]}, {"point": [-969.69, -890.2]}, {"point": [-969.69, -801.74], "toTop": true, "allowedToTop": true}]}}]}}, "nodeID": "66c59d7a2825ec011ad62380"}, "66c59dd22825ec011ad62388": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c59dd22825ec011ad62389"}}, "builtIn": {}, "dynamic": []}, "messageID": "66c59dd76f3f9b24e0b43c70"}, "nodeID": "66c59dd22825ec011ad62388"}, "66c59dd22825ec011ad6238a": {"type": "block", "data": {"name": "New Block 16", "steps": ["66c59dd22825ec011ad62388", "66c59dff2825ec011ad62391"]}, "nodeID": "66c59dd22825ec011ad6238a", "coords": [319.7769334242319, -737.686150334715]}, "66c59dff2825ec011ad62391": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "gf13l3cvj", "name": "1", "actions": []}, {"id": "331423cvp", "name": "2", "actions": []}, {"id": "gm14g3c9p", "name": "3", "actions": []}, {"id": "7q14r3ckz", "name": "4", "actions": []}, {"id": "b01583cjy", "name": "5", "actions": []}, {"id": "q415l3c88", "name": "Show more...", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c59e002825ec011ad62393"}}, "dynamic": [{"type": "", "target": "66c59f5c2825ec011ad62407", "id": "66c59e002825ec011ad62392", "data": {}}, {"id": "66c59e072825ec011ad62396", "type": "", "target": "66c59f6a2825ec011ad6240d", "data": {}}, {"id": "66c59e0a2825ec011ad62399", "type": "", "target": "66c59f762825ec011ad62413", "data": {}}, {"id": "66c59e0d2825ec011ad6239b", "type": "", "target": "66c59f822825ec011ad62419", "data": {}}, {"id": "66c59e102825ec011ad6239d", "type": "", "target": "66c59f8e2825ec011ad6241f", "data": {}}, {"id": "66c59e172825ec011ad6239f", "type": "", "target": "66c59e362825ec011ad623bf", "data": {}}]}}, "nodeID": "66c59dff2825ec011ad62391"}, "66c59e362825ec011ad623bf": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "gf13l3cvj", "name": "6", "actions": []}, {"id": "331423cvp", "name": "7", "actions": []}, {"id": "gm14g3c9p", "name": "8", "actions": []}, {"id": "7q14r3ckz", "name": "9", "actions": []}, {"id": "b01583cjy", "name": "10", "actions": []}, {"id": "q415l3c88", "name": "Show more...", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c59e362825ec011ad623c0"}}, "dynamic": [{"type": "", "target": "66c59fa02825ec011ad62425", "id": "66c59e362825ec011ad623c1", "data": {}}, {"type": "", "target": "66c59fab2825ec011ad6242b", "id": "66c59e362825ec011ad623c2", "data": {}}, {"type": "", "target": "66c59fbe2825ec011ad62431", "id": "66c59e362825ec011ad623c3", "data": {}}, {"type": "", "target": "66c59fcc2825ec011ad62437", "id": "66c59e362825ec011ad623c4", "data": {}}, {"type": "", "target": "66c59fd92825ec011ad6243d", "id": "66c59e362825ec011ad623c5", "data": {}}, {"type": "", "target": "66c59e592825ec011ad623df", "id": "66c59e362825ec011ad623c6", "data": {"points": [{"point": [1046.33, 9.68]}, {"point": [1299.44, 9.68]}, {"point": [1299.44, -264.37]}, {"point": [1552.56, -264.37], "allowedToTop": false}]}}]}}, "nodeID": "66c59e362825ec011ad623bf"}, "66c59e362825ec011ad623c8": {"type": "block", "data": {"name": "New Block 16 copy", "color": "", "steps": ["66c59e362825ec011ad623bf"]}, "nodeID": "66c59e362825ec011ad623c8", "coords": [879.4447456692437, -338.87617312064685]}, "66c59e592825ec011ad623df": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "gf13l3cvj", "name": "11", "actions": []}, {"id": "331423cvp", "name": "12", "actions": []}, {"id": "gm14g3c9p", "name": "13", "actions": []}, {"id": "7q14r3ckz", "name": "14", "actions": []}, {"id": "b01583cjy", "name": "15", "actions": []}, {"id": "q415l3c88", "name": "", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c59e592825ec011ad623e0"}}, "dynamic": [{"type": "", "target": "66c59ff02825ec011ad62443", "id": "66c59e592825ec011ad623e1", "data": {}}, {"type": "", "target": "66c59fff2825ec011ad62449", "id": "66c59e592825ec011ad623e2", "data": {}}, {"type": "", "target": "66c5a00c2825ec011ad6244f", "id": "66c59e592825ec011ad623e3", "data": {}}, {"type": "", "target": "66c5a0192825ec011ad62455", "id": "66c59e592825ec011ad623e4", "data": {}}, {"type": "", "target": "66c5a0252825ec011ad6245b", "id": "66c59e592825ec011ad623e5", "data": {}}, {"type": "", "target": null, "id": "66c59e592825ec011ad623e6"}]}}, "nodeID": "66c59e592825ec011ad623df"}, "66c59e592825ec011ad623e8": {"type": "block", "data": {"name": "New Block 16 copy copy", "color": "", "steps": ["66c59e592825ec011ad623df"]}, "nodeID": "66c59e592825ec011ad623e8", "coords": [1719.4447456692437, -342.48117856371584]}, "66c59f6a2825ec011ad6240c": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59f6a2825ec011ad6240c"}, "66c59f6a2825ec011ad6240d": {"type": "actions", "data": {"name": "", "steps": ["66d5af27c0eb025abfab4fb7", "66c59f6a2825ec011ad6240c"]}, "nodeID": "66c59f6a2825ec011ad6240d", "coords": [0, 0]}, "66c59f762825ec011ad62412": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59f762825ec011ad62412"}, "66c59f762825ec011ad62413": {"type": "actions", "data": {"name": "", "steps": ["66d5af55c0eb025abfab4fba", "66c59f762825ec011ad62412"]}, "nodeID": "66c59f762825ec011ad62413", "coords": [0, 0]}, "66c59f822825ec011ad62418": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59f822825ec011ad62418"}, "66c59f822825ec011ad62419": {"type": "actions", "data": {"name": "", "steps": ["66d5af78c0eb025abfab4fbd", "66c59f822825ec011ad62418"]}, "nodeID": "66c59f822825ec011ad62419", "coords": [0, 0]}, "66c59f8e2825ec011ad6241e": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59f8e2825ec011ad6241e"}, "66c59f8e2825ec011ad6241f": {"type": "actions", "data": {"name": "", "steps": ["66d5af9ac0eb025abfab4fc0", "66c59f8e2825ec011ad6241e"]}, "nodeID": "66c59f8e2825ec011ad6241f", "coords": [0, 0]}, "66c59fa02825ec011ad62424": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fa02825ec011ad62424"}, "66c59fa02825ec011ad62425": {"type": "actions", "data": {"name": "", "steps": ["66d5afc3c0eb025abfab4fc3", "66c59fa02825ec011ad62424"]}, "nodeID": "66c59fa02825ec011ad62425", "coords": [0, 0]}, "66c59fab2825ec011ad6242a": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fab2825ec011ad6242a"}, "66c59fab2825ec011ad6242b": {"type": "actions", "data": {"name": "", "steps": ["66d5afdec0eb025abfab4fc6", "66c59fab2825ec011ad6242a"]}, "nodeID": "66c59fab2825ec011ad6242b", "coords": [0, 0]}, "66c59fbe2825ec011ad62430": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fbe2825ec011ad62430"}, "66c59fbe2825ec011ad62431": {"type": "actions", "data": {"name": "", "steps": ["66d5affac0eb025abfab4fc9", "66c59fbe2825ec011ad62430"]}, "nodeID": "66c59fbe2825ec011ad62431", "coords": [0, 0]}, "66c59fcc2825ec011ad62436": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fcc2825ec011ad62436"}, "66c59fcc2825ec011ad62437": {"type": "actions", "data": {"name": "", "steps": ["66d5b024c0eb025abfab4fcc", "66c59fcc2825ec011ad62436"]}, "nodeID": "66c59fcc2825ec011ad62437", "coords": [0, 0]}, "66c59fd92825ec011ad6243c": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fd92825ec011ad6243c"}, "66c59fd92825ec011ad6243d": {"type": "actions", "data": {"name": "", "steps": ["66d5b049c0eb025abfab4fcf", "66c59fd92825ec011ad6243c"]}, "nodeID": "66c59fd92825ec011ad6243d", "coords": [0, 0]}, "66c59ff02825ec011ad62442": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59ff02825ec011ad62442"}, "66c59ff02825ec011ad62443": {"type": "actions", "data": {"name": "", "steps": ["66d5b075c0eb025abfab4fd2", "66c59ff02825ec011ad62442"]}, "nodeID": "66c59ff02825ec011ad62443", "coords": [0, 0]}, "66c59fff2825ec011ad62448": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59fff2825ec011ad62448"}, "66c59fff2825ec011ad62449": {"type": "actions", "data": {"name": "", "steps": ["66d5b098c0eb025abfab4fd5", "66c59fff2825ec011ad62448"]}, "nodeID": "66c59fff2825ec011ad62449", "coords": [0, 0]}, "66c5a00c2825ec011ad6244e": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a00c2825ec011ad6244e"}, "66c5a00c2825ec011ad6244f": {"type": "actions", "data": {"name": "", "steps": ["66d5b0c5c0eb025abfab4fd8", "66c5a00c2825ec011ad6244e"]}, "nodeID": "66c5a00c2825ec011ad6244f", "coords": [0, 0]}, "66c5a0192825ec011ad62454": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a0192825ec011ad62454"}, "66c5a0192825ec011ad62455": {"type": "actions", "data": {"name": "", "steps": ["66d5b0e0c0eb025abfab4fdb", "66c5a0192825ec011ad62454"]}, "nodeID": "66c5a0192825ec011ad62455", "coords": [0, 0]}, "66c5a0252825ec011ad6245a": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a0252825ec011ad6245a"}, "66c5a0252825ec011ad6245b": {"type": "actions", "data": {"name": "", "steps": ["66d5b0fac0eb025abfab4fde", "66c5a0252825ec011ad6245a"]}, "nodeID": "66c5a0252825ec011ad6245b", "coords": [0, 0]}, "66c5a1d22825ec011ad62461": {"type": "message", "data": {"name": "", "messageID": "66c5a1d26f3f9b24e0b43ecc", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a2f52825ec011ad62482", "id": "66c5a1d22825ec011ad62462", "data": {"points": [{"point": [1297.52, -982.97], "reversed": true}, {"point": [1191.57, -982.97]}, {"point": [1191.57, -866.55], "toTop": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a1d22825ec011ad62461"}, "66c5a1d22825ec011ad62463": {"type": "block", "data": {"name": "Prompt user for time", "steps": ["66c5a1d22825ec011ad62461"], "color": ""}, "nodeID": "66c5a1d22825ec011ad62463", "coords": [1464.412321372727, -1070.0744742415259]}, "66c5a20d2825ec011ad6246c": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "oc22h3cto", "name": "12:30 PM noon", "actions": []}, {"id": "8c2343cqf", "name": "12:45 PM noon", "actions": []}, {"id": "k723l3ca4", "name": "13:00 PM", "actions": []}, {"id": "aq2403cbd", "name": "13:45 PM", "actions": []}, {"id": "h224d3crh", "name": "14:00PM", "actions": []}, {"id": "e32533cap", "name": "Other available times", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a20d2825ec011ad6246e"}}, "dynamic": [{"type": "", "target": "66c5a74d2825ec011ad62584", "id": "66c5a20d2825ec011ad6246d", "data": {}}, {"id": "66c5a2412825ec011ad62471", "type": "", "target": "66c5a7592825ec011ad6258a", "data": {}}, {"id": "66c5a2922825ec011ad62474", "type": "", "target": "66c5a7682825ec011ad62590", "data": {}}, {"id": "66c5a2af2825ec011ad62476", "type": "", "target": "66c5ab102825ec011ad625e6", "data": {}}, {"id": "66c5a2b62825ec011ad62478", "type": "", "target": "66c5ab1d2825ec011ad625ec", "data": {}}, {"id": "66c5a2df2825ec011ad6247a", "type": "", "target": "66c5a2fc2825ec011ad62498", "data": {"points": [{"point": [1358.46, -517.99]}, {"point": [1544.58, -517.99]}, {"point": [1544.58, -796.14]}, {"point": [1730.69, -796.14], "allowedToTop": false}]}}]}}, "nodeID": "66c5a20d2825ec011ad6246c"}, "66c5a2f52825ec011ad62482": {"type": "block", "data": {"name": "New Block 18", "steps": ["66c5a20d2825ec011ad6246c"]}, "nodeID": "66c5a2f52825ec011ad62482", "coords": [1191.5674247396198, -866.5489504778088]}, "66c5a2fc2825ec011ad62498": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "oc22h3cto", "name": "14:15 PM", "actions": []}, {"id": "8c2343cqf", "name": "14:30 PM noon", "actions": []}, {"id": "k723l3ca4", "name": "14:45 PM", "actions": []}, {"id": "aq2403cbd", "name": "15:00 PM", "actions": []}, {"id": "h224d3crh", "name": "15:15PM", "actions": []}, {"id": "e32533cap", "name": "Other available times", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a2fc2825ec011ad62499"}}, "dynamic": [{"type": "", "target": "66c5a6922825ec011ad62566", "id": "66c5a2fc2825ec011ad6249a", "data": {}}, {"type": "", "target": "66c5a6a32825ec011ad6256c", "id": "66c5a2fc2825ec011ad6249b", "data": {}}, {"type": "", "target": "66c5a6b22825ec011ad62572", "id": "66c5a2fc2825ec011ad6249c", "data": {}}, {"type": "", "target": "66c5a6c32825ec011ad62578", "id": "66c5a2fc2825ec011ad6249d", "data": {}}, {"type": "", "target": "66c5a6d02825ec011ad6257e", "id": "66c5a2fc2825ec011ad6249e", "data": {}}, {"type": "", "target": "66c5a3002825ec011ad624af", "id": "66c5a2fc2825ec011ad6249f", "data": {"points": [{"point": [2064.47, -525.69]}, {"point": [2163.92, -525.69]}, {"point": [2163.92, -829.7]}, {"point": [2263.36, -829.7], "allowedToTop": false}]}}]}}, "nodeID": "66c5a2fc2825ec011ad62498"}, "66c5a2fc2825ec011ad624a1": {"type": "block", "data": {"name": "New Block 18 copy", "color": "", "steps": ["66c5a2fc2825ec011ad62498"]}, "nodeID": "66c5a2fc2825ec011ad624a1", "coords": [1897.5833250963349, -874.2527867074916]}, "66c5a3002825ec011ad624af": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "oc22h3cto", "name": "15:30 PM noon", "actions": []}, {"id": "8c2343cqf", "name": "15:45 PM noon", "actions": []}, {"id": "k723l3ca4", "name": "16:00 PM", "actions": []}, {"id": "aq2403cbd", "name": "16:45 PM", "actions": []}, {"id": "h224d3crh", "name": "17:00PM", "actions": []}, {"id": "e32533cap", "name": "Other available times", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a3002825ec011ad624b0"}}, "dynamic": [{"type": "", "target": "66c5a6262825ec011ad62548", "id": "66c5a3002825ec011ad624b1", "data": {}}, {"type": "", "target": "66c5a6392825ec011ad6254e", "id": "66c5a3002825ec011ad624b2", "data": {}}, {"type": "", "target": "66c5a6552825ec011ad62554", "id": "66c5a3002825ec011ad624b3", "data": {}}, {"type": "", "target": "66c5a6632825ec011ad6255a", "id": "66c5a3002825ec011ad624b4", "data": {}}, {"type": "", "target": "66c5a6712825ec011ad62560", "id": "66c5a3002825ec011ad624b5", "data": {}}, {"type": "", "target": "66c5a41b2825ec011ad624ed", "id": "66c5a3002825ec011ad624b6", "data": {"points": [{"point": [2597.14, -559.25]}, {"point": [2661.25, -559.25]}, {"point": [2661.25, -828.77]}, {"point": [2725.35, -828.77], "allowedToTop": false}]}}]}}, "nodeID": "66c5a3002825ec011ad624af"}, "66c5a3002825ec011ad624b8": {"type": "block", "data": {"name": "New Block 18 copy copy", "color": "", "steps": ["66c5a3002825ec011ad624af"]}, "nodeID": "66c5a3002825ec011ad624b8", "coords": [2430.2488543819836, -907.8119636802502]}, "66c5a3fa2825ec011ad624d5": {"type": "block", "data": {"name": "New Block 18 copy copy", "color": "", "steps": ["66c5a3fa2825ec011ad624d7"]}, "nodeID": "66c5a3fa2825ec011ad624d5", "coords": [3347.0964076351574, -914.983878331973]}, "66c5a3fa2825ec011ad624d7": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "oc22h3cto", "name": "18:30 PM noon", "actions": []}, {"id": "8c2343cqf", "name": "18:45 PM noon", "actions": []}, {"id": "k723l3ca4", "name": "19:00 PM", "actions": []}, {"id": "aq2403cbd", "name": "19:15 PM", "actions": []}, {"id": "h224d3crh", "name": "19:30PM", "actions": []}, {"id": "e32533cap", "name": "", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a3fa2825ec011ad624d8"}}, "dynamic": [{"type": "", "target": "66d5b1fac0eb025abfab4fe4", "id": "66c5a3fa2825ec011ad624d9", "data": {"points": [{"point": [3180.21, -836.63], "reversed": true}, {"point": [3122.28, -836.63]}, {"point": [3122.28, -448.94], "toTop": true, "allowedToTop": true}]}}, {"type": "", "target": "66d5b1fac0eb025abfab4fe4", "id": "66c5a3fa2825ec011ad624da", "data": {"points": [{"point": [3180.21, -782.4], "reversed": true}, {"point": [3122.28, -782.4]}, {"point": [3122.28, -448.94], "toTop": true, "allowedToTop": true}]}}, {"type": "", "target": "66d5b1fac0eb025abfab4fe4", "id": "66c5a3fa2825ec011ad624db", "data": {"points": [{"point": [3180.21, -728.4], "reversed": true}, {"point": [3122.28, -728.4]}, {"point": [3122.28, -448.94], "toTop": true, "allowedToTop": true}]}}, {"type": "", "target": "66d5b1fac0eb025abfab4fe4", "id": "66c5a3fa2825ec011ad624dc", "data": {"points": [{"point": [3180.21, -674.41], "reversed": true}, {"point": [3122.28, -674.41]}, {"point": [3122.28, -448.94], "toTop": true, "allowedToTop": true}]}}, {"type": "", "target": "66d5b1fac0eb025abfab4fe4", "id": "66c5a3fa2825ec011ad624dd", "data": {"points": [{"point": [3180.21, -620.42], "reversed": true}, {"point": [3122.28, -620.42]}, {"point": [3122.28, -448.94], "toTop": true, "allowedToTop": true}]}}, {"type": "", "target": null, "id": "66c5a3fa2825ec011ad624de"}]}}, "nodeID": "66c5a3fa2825ec011ad624d7"}, "66c5a4d82825ec011ad62501": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c5a4d82825ec011ad62502"}}, "builtIn": {}, "dynamic": []}, "messageID": "66c5a4de6f3f9b24e0b440e0"}, "nodeID": "66c5a4d82825ec011ad62501"}, "66c5a4d82825ec011ad62503": {"type": "block", "data": {"name": "Deposit notice", "steps": ["66c5a4d82825ec011ad62501", "66c5a7802825ec011ad62595", "66c5a7a42825ec011ad6259a"], "color": ""}, "nodeID": "66c5a4d82825ec011ad62503", "coords": [3932.9844310116223, -857.5213344407061]}, "66c5a41b2825ec011ad624eb": {"type": "block", "data": {"name": "New Block 18 copy", "color": "", "steps": ["66c5a41b2825ec011ad624ed"]}, "nodeID": "66c5a41b2825ec011ad624eb", "coords": [2890.992520042624, -905.626505932987]}, "66c5a41b2825ec011ad624ed": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "oc22h3cto", "name": "17:15 PM", "actions": []}, {"id": "8c2343cqf", "name": "17:30 PM noon", "actions": []}, {"id": "k723l3ca4", "name": "17:45 PM", "actions": []}, {"id": "aq2403cbd", "name": "18:00 PM", "actions": []}, {"id": "h224d3crh", "name": "18:15PM", "actions": []}, {"id": "e32533cap", "name": "Other available times", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "noReply": null, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a41b2825ec011ad624ee"}}, "dynamic": [{"type": "", "target": "66c5a59a2825ec011ad6250f", "id": "66c5a41b2825ec011ad624ef"}, {"type": "", "target": "66c5a5a92825ec011ad62515", "id": "66c5a41b2825ec011ad624f0"}, {"type": "", "target": "66c5a5bf2825ec011ad6251b", "id": "66c5a41b2825ec011ad624f1"}, {"type": "", "target": "66c5a5de2825ec011ad62521", "id": "66c5a41b2825ec011ad624f2"}, {"type": "", "target": "66c5a5ed2825ec011ad62527", "id": "66c5a41b2825ec011ad624f3"}, {"type": "", "target": "66c5a3fa2825ec011ad624d7", "id": "66c5a41b2825ec011ad624f4", "data": {"points": [{"point": [3057.88, -557.07]}, {"point": [3119.04, -557.07]}, {"point": [3119.04, -836.87]}, {"point": [3180.21, -836.87], "allowedToTop": false}]}}]}}, "nodeID": "66c5a41b2825ec011ad624ed"}, "66c5a59a2825ec011ad6250e": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a59a2825ec011ad6250e"}, "66c5a59a2825ec011ad6250f": {"type": "actions", "data": {"name": "Actions", "steps": ["66c5a59a2825ec011ad6250e"]}, "nodeID": "66c5a59a2825ec011ad6250f", "coords": [0, 0]}, "66c5a5a92825ec011ad62514": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a5a92825ec011ad62514"}, "66c5a5a92825ec011ad62515": {"type": "actions", "data": {"name": "Actions", "steps": ["66c5a5a92825ec011ad62514"]}, "nodeID": "66c5a5a92825ec011ad62515", "coords": [0, 0]}, "66c5a5bf2825ec011ad6251a": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a5bf2825ec011ad6251a"}, "66c5a5bf2825ec011ad6251b": {"type": "actions", "data": {"name": "Actions", "steps": ["66c5a5bf2825ec011ad6251a"]}, "nodeID": "66c5a5bf2825ec011ad6251b", "coords": [0, 0]}, "66c5a5de2825ec011ad62520": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a5de2825ec011ad62520"}, "66c5a5de2825ec011ad62521": {"type": "actions", "data": {"name": "Actions", "steps": ["66c5a5de2825ec011ad62520"]}, "nodeID": "66c5a5de2825ec011ad62521", "coords": [0, 0]}, "66c5a5ed2825ec011ad62526": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a5ed2825ec011ad62526"}, "66c5a5ed2825ec011ad62527": {"type": "actions", "data": {"name": "Actions", "steps": ["66c5a5ed2825ec011ad62526"]}, "nodeID": "66c5a5ed2825ec011ad62527", "coords": [0, 0]}, "66c5a6262825ec011ad62547": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6262825ec011ad62547"}, "66c5a6262825ec011ad62548": {"type": "actions", "data": {"name": "", "steps": ["66c5a6262825ec011ad62547"]}, "nodeID": "66c5a6262825ec011ad62548", "coords": [0, 0]}, "66c5a6392825ec011ad6254d": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6392825ec011ad6254d"}, "66c5a6392825ec011ad6254e": {"type": "actions", "data": {"name": "", "steps": ["66c5a6392825ec011ad6254d"]}, "nodeID": "66c5a6392825ec011ad6254e", "coords": [0, 0]}, "66c5a6552825ec011ad62553": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6552825ec011ad62553"}, "66c5a6552825ec011ad62554": {"type": "actions", "data": {"name": "", "steps": ["66c5a6552825ec011ad62553"]}, "nodeID": "66c5a6552825ec011ad62554", "coords": [0, 0]}, "66c5a6632825ec011ad62559": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6632825ec011ad62559"}, "66c5a6632825ec011ad6255a": {"type": "actions", "data": {"name": "", "steps": ["66c5a6632825ec011ad62559"]}, "nodeID": "66c5a6632825ec011ad6255a", "coords": [0, 0]}, "66c5a6712825ec011ad6255f": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6712825ec011ad6255f"}, "66c5a6712825ec011ad62560": {"type": "actions", "data": {"name": "", "steps": ["66c5a6712825ec011ad6255f"]}, "nodeID": "66c5a6712825ec011ad62560", "coords": [0, 0]}, "66c5a6922825ec011ad62565": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6922825ec011ad62565"}, "66c5a6922825ec011ad62566": {"type": "actions", "data": {"name": "", "steps": ["66c5a6922825ec011ad62565"]}, "nodeID": "66c5a6922825ec011ad62566", "coords": [0, 0]}, "66c5a6a32825ec011ad6256b": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6a32825ec011ad6256b"}, "66c5a6a32825ec011ad6256c": {"type": "actions", "data": {"name": "", "steps": ["66c5a6a32825ec011ad6256b"]}, "nodeID": "66c5a6a32825ec011ad6256c", "coords": [0, 0]}, "66c5a6b22825ec011ad62571": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6b22825ec011ad62571"}, "66c5a6b22825ec011ad62572": {"type": "actions", "data": {"name": "", "steps": ["66c5a6b22825ec011ad62571"]}, "nodeID": "66c5a6b22825ec011ad62572", "coords": [0, 0]}, "66c5a6c32825ec011ad62577": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6c32825ec011ad62577"}, "66c5a6c32825ec011ad62578": {"type": "actions", "data": {"name": "", "steps": ["66c5a6c32825ec011ad62577"]}, "nodeID": "66c5a6c32825ec011ad62578", "coords": [0, 0]}, "66c5a6d02825ec011ad6257d": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a6d02825ec011ad6257d"}, "66c5a6d02825ec011ad6257e": {"type": "actions", "data": {"name": "", "steps": ["66c5a6d02825ec011ad6257d"]}, "nodeID": "66c5a6d02825ec011ad6257e", "coords": [0, 0]}, "66c5a74d2825ec011ad62583": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a74d2825ec011ad62583"}, "66c5a74d2825ec011ad62584": {"type": "actions", "data": {"name": "", "steps": ["66c5a74d2825ec011ad62583"]}, "nodeID": "66c5a74d2825ec011ad62584", "coords": [0, 0]}, "66c5a7592825ec011ad62589": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a7592825ec011ad62589"}, "66c5a7592825ec011ad6258a": {"type": "actions", "data": {"name": "", "steps": ["66c5a7592825ec011ad62589"]}, "nodeID": "66c5a7592825ec011ad6258a", "coords": [0, 0]}, "66c5a7682825ec011ad6258f": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a7682825ec011ad6258f"}, "66c5a7682825ec011ad62590": {"type": "actions", "data": {"name": "", "steps": ["66c5a7682825ec011ad6258f"]}, "nodeID": "66c5a7682825ec011ad62590", "coords": [0, 0]}, "66c5a7802825ec011ad62595": {"type": "message", "data": {"name": "", "messageID": "66c5a7806f3f9b24e0b44358", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c5a7802825ec011ad62596"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a7802825ec011ad62595"}, "66c5a7a42825ec011ad6259a": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "hz38p3c42", "name": "Yes", "actions": []}, {"id": "313953crr", "name": "No", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a7a42825ec011ad6259c"}}, "dynamic": [{"type": "", "target": "66d5cd11176e7b205fc4c2fd", "id": "66c5a7a42825ec011ad6259b", "data": {"points": [{"point": [3766.1, -623.23], "reversed": true}, {"point": [3645.61, -623.23]}, {"point": [3645.61, -1175.51]}, {"point": [3525.12, -1175.51], "reversed": true, "allowedToTop": true}]}}, {"id": "66c5a7aa2825ec011ad6259f", "type": "", "target": "66c5a8332825ec011ad625bb", "data": {"points": [{"point": [4099.87, -569]}, {"point": [4374.48, -569]}, {"point": [4374.48, -418.29]}, {"point": [4649.08, -418.29], "allowedToTop": true}]}}]}}, "nodeID": "66c5a7a42825ec011ad6259a", "coords": [0, 0]}, "66c5a7be2825ec011ad625a2": {"type": "message", "data": {"name": "", "messageID": "66c5a7be6f3f9b24e0b443aa", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a8102825ec011ad625b2", "id": "66c5a7be2825ec011ad625a3", "data": {"points": [{"point": [5397.33, -851.81]}, {"point": [5442, -851.81]}, {"point": [5442, -729.4]}, {"point": [5486.66, -729.4], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a7be2825ec011ad625a2", "coords": [0, 0]}, "66c5a7be2825ec011ad625a4": {"type": "block", "data": {"name": "Chat resumption", "steps": ["66c5a7be2825ec011ad625a2"], "color": ""}, "nodeID": "66c5a7be2825ec011ad625a4", "coords": [5230.4387331853095, -968.9083332552533]}, "66c5a8102825ec011ad625b0": {"type": "trace", "data": {"name": "Stripe on payment", "_v": 1, "paths": [{"label": "Check payment confirmation", "isDefault": true}], "payload": {"name": "Stripe on payment", "body": "", "bodyType": "json", "scope": "local", "isBlocking": true}, "defaultPath": 0, "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": [{"type": "", "target": "66c5f5f7b9205a485a70e987", "id": "66c5a8102825ec011ad625b1", "data": {"points": [{"point": [5817.84, -621.95]}, {"point": [5898.63, -621.95]}, {"point": [5898.63, -701.65]}, {"point": [5979.41, -701.65], "allowedToTop": true}]}}]}}, "nodeID": "66c5a8102825ec011ad625b0", "coords": [0, 0]}, "66c5a8102825ec011ad625b2": {"type": "block", "data": {"name": "New Block 25", "steps": ["66c5a8102825ec011ad625b0"]}, "nodeID": "66c5a8102825ec011ad625b2", "coords": [5651.662581188623, -756.4026273891399]}, "66c5a8332825ec011ad625b9": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c5a8332825ec011ad625ba"}}, "builtIn": {}, "dynamic": []}, "messageID": "66c5a83d6f3f9b24e0b44477"}, "nodeID": "66c5a8332825ec011ad625b9"}, "66c5a88d2825ec011ad625c2": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c5a88d2825ec011ad625c3"}}, "builtIn": {}, "dynamic": []}, "messageID": "66c5a8e86f3f9b24e0b44503"}, "nodeID": "66c5a88d2825ec011ad625c2"}, "66c5a9052825ec011ad625c7": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "pw3j73c2d", "name": "Yes", "actions": []}, {"id": "233jp3c89", "name": "No", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5a9052825ec011ad625c9"}}, "dynamic": [{"type": "", "target": "66c5a9182825ec011ad625d0", "id": "66c5a9052825ec011ad625c8", "data": {}}, {"id": "66c5a90b2825ec011ad625cc", "type": "", "target": "66c5a9272825ec011ad625d5", "data": {"points": [{"point": [4980.97, -136.77]}, {"point": [5066.37, -136.77]}, {"point": [5066.37, -108.16]}, {"point": [5151.77, -108.16], "allowedToTop": false}]}}]}}, "nodeID": "66c5a9052825ec011ad625c7"}, "66c5a9182825ec011ad625cf": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5f992ac196950e2c1e5d3", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a9182825ec011ad625cf"}, "66c5a9182825ec011ad625d0": {"type": "actions", "data": {"name": "", "steps": ["66c5a9182825ec011ad625cf"]}, "nodeID": "66c5a9182825ec011ad625d0", "coords": [0, 0]}, "66c5a9272825ec011ad625d5": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a9552825ec011ad625df", "id": "66c5a9272825ec011ad625d6", "data": {}}}, "builtIn": {}, "dynamic": []}, "messageID": "66c5a92d6f3f9b24e0b44565"}, "nodeID": "66c5a9272825ec011ad625d5"}, "66c5a9272825ec011ad625d7": {"type": "block", "data": {"name": "New Block 27", "steps": ["66c5a9272825ec011ad625d5"]}, "nodeID": "66c5a9272825ec011ad625d7", "coords": [5318.658278210108, -186.27133444070608]}, "66c5a9552825ec011ad625de": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5a9552825ec011ad625de"}, "66c5a9552825ec011ad625df": {"type": "actions", "data": {"name": "", "steps": ["66c5a9552825ec011ad625de"]}, "nodeID": "66c5a9552825ec011ad625df", "coords": [0, 0]}, "66c5ab102825ec011ad625e5": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5ab102825ec011ad625e5"}, "66c5ab102825ec011ad625e6": {"type": "actions", "data": {"name": "", "steps": ["66c5ab102825ec011ad625e5"]}, "nodeID": "66c5ab102825ec011ad625e6", "coords": [0, 0]}, "66c5ab1d2825ec011ad625eb": {"type": "goToNode", "data": {"name": "", "nodeID": "66d5b1fac0eb025abfab4fe4", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5ab1d2825ec011ad625eb"}, "66c5ab1d2825ec011ad625ec": {"type": "actions", "data": {"name": "", "steps": ["66c5ab1d2825ec011ad625eb"]}, "nodeID": "66c5ab1d2825ec011ad625ec", "coords": [0, 0]}, "66c5f472b9205a485a70e966": {"type": "api", "data": {"name": "", "url": "https://[your endpoint subdomain].pythonanywhere.com/create-checkout-session/{{[amount].66d5c41e7f0b84ecf05108eb}}/", "body": [], "params": [], "method": "POST", "headers": [], "content": "", "mappings": [{"path": "url", "var": "66be69cd0d3bb525af0538ee"}, {"path": "sessionid", "var": "66c5f54c27b27f078d3e6f62"}], "bodyType": "formData", "selectedAction": "Make a POST Request", "selectedIntegration": "Custom API", "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66cf37cf2b174507e0a525d6", "id": "66c5f472b9205a485a70e967", "data": {}}, "fail": {"type": "fail", "target": "66c5f49cb9205a485a70e973", "id": "66c5f472b9205a485a70e968", "data": {}}}, "dynamic": []}}, "nodeID": "66c5f472b9205a485a70e966"}, "66c5f472b9205a485a70e969": {"type": "block", "data": {"name": "Create payment session", "steps": ["66c5f472b9205a485a70e966"], "color": ""}, "nodeID": "66c5f472b9205a485a70e969", "coords": [5034.776933424232, -731.436150334715]}, "66c5f5f7b9205a485a70e984": {"type": "api", "data": {"name": "", "url": "https://[your endpoint subdomain].pythonanywhere.com/confirm-payment/{{[stripe_session_id].66c5f54c27b27f078d3e6f62}}/", "body": [], "params": [], "method": "GET", "headers": [], "content": "", "mappings": [{"path": "payment_status", "var": "66c5f6b527b27f078d3e7077"}], "bodyType": "formData", "selectedAction": "Make a GET Request", "selectedIntegration": "Custom API", "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66c5f6cbb9205a485a70e9a3", "id": "66c5f5f7b9205a485a70e985", "data": {"points": [{"point": [6310.59, -558.16]}, {"point": [6422.91, -558.16]}, {"point": [6422.91, -575.67]}, {"point": [6535.22, -575.67], "allowedToTop": true}]}}, "fail": {"type": "fail", "target": "66c5f65cb9205a485a70e99b", "id": "66c5f5f7b9205a485a70e986", "data": {}}}, "dynamic": []}}, "nodeID": "66c5f5f7b9205a485a70e984"}, "66c5f5f7b9205a485a70e987": {"type": "block", "data": {"name": "New Block 30", "steps": ["66c5f5f7b9205a485a70e984"]}, "nodeID": "66c5f5f7b9205a485a70e987", "coords": [6144.412581188623, -728.65262738914]}, "66c5f65cb9205a485a70e99a": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5f5f7b9205a485a70e987", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5f65cb9205a485a70e99a"}, "66c5f65cb9205a485a70e99b": {"type": "actions", "data": {"name": "", "steps": ["66c5f65cb9205a485a70e99a"]}, "nodeID": "66c5f65cb9205a485a70e99b", "coords": [0, 0]}, "66c5f6cbb9205a485a70e9a0": {"type": "ifV2", "data": {"name": "If", "expressions": [{"type": null, "name": "Check Payment status ", "value": [{"logicInterface": "variable", "type": "equals", "value": [{"type": "variable", "value": "66c5f6b527b27f078d3e7077"}, {"type": "value", "value": "paid"}]}]}], "noMatch": {"type": "path", "pathName": "Failed payment"}, "portsV2": {"byKey": {}, "builtIn": {"else": {"id": "66c5f708b9205a485a70e9aa", "type": "else", "target": "66c5f762b9205a485a70e9bd", "data": {"points": [{"point": [6867.11, -469.84]}, {"point": [7037.47, -469.84]}, {"point": [7037.47, -371.65]}, {"point": [7207.82, -371.65], "allowedToTop": true}]}}}, "dynamic": [{"type": "", "target": "66da147244e67d133b44d985", "id": "66c5f6cbb9205a485a70e9a1", "data": {"points": [{"point": [6866.4, -523.61]}, {"point": [6977.6, -523.61]}, {"point": [6977.6, -654.49]}, {"point": [7088.8, -654.49], "allowedToTop": true}]}}]}}, "nodeID": "66c5f6cbb9205a485a70e9a0"}, "66c5f6cbb9205a485a70e9a3": {"type": "block", "data": {"name": "New Block 32", "steps": ["66c5f6cbb9205a485a70e9a0"]}, "nodeID": "66c5f6cbb9205a485a70e9a3", "coords": [6700.220987080055, -602.6684625208233]}, "66c5f762b9205a485a70e9bb": {"type": "message", "data": {"name": "", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66c5f762b9205a485a70e9bc"}}, "builtIn": {}, "dynamic": []}, "messageID": "66c5f77527b27f078d3e7137"}, "nodeID": "66c5f762b9205a485a70e9bb"}, "66c5f762b9205a485a70e9bd": {"type": "block", "data": {"name": "New Block 34", "steps": ["66c5f762b9205a485a70e9bb", "66c5fac1ac196950e2c1e5fa"]}, "nodeID": "66c5f762b9205a485a70e9bd", "coords": [7372.822005016748, -398.65262738914]}, "66c5f992ac196950e2c1e5d0": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "wx8i3fj7", "name": "Make Payment", "actions": []}, {"id": "s59u3ftx", "name": "Cancel booking", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5f992ac196950e2c1e5d2"}}, "dynamic": [{"type": "", "target": "66c5fa3cac196950e2c1e5f1", "id": "66c5f992ac196950e2c1e5d1", "data": {}}, {"id": "66c5f9bbac196950e2c1e5d9", "type": "", "target": "66c5f9e7ac196950e2c1e5df", "data": {"points": [{"point": [4722.88, -1205.09]}, {"point": [5044.31, -1212.54]}]}}]}}, "nodeID": "66c5f992ac196950e2c1e5d0", "coords": [0, 0]}, "66c5f992ac196950e2c1e5d3": {"type": "block", "data": {"name": "Chat revisit notice", "steps": ["66cf35ee2b174507e0a525c5", "66cf36562b174507e0a525c9"], "color": ""}, "nodeID": "66c5f992ac196950e2c1e5d3", "coords": [4547.205620004812, -927.712375040968]}, "66c5f9e7ac196950e2c1e5dd": {"type": "message", "data": {"name": "", "messageID": "66c5f9e927b27f078d3e7243", "draft": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5fa29ac196950e2c1e5eb", "id": "66c5f9e7ac196950e2c1e5de", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5f9e7ac196950e2c1e5dd"}, "66c5f9e7ac196950e2c1e5df": {"type": "block", "data": {"name": "Cancel payment", "steps": ["66c5f9e7ac196950e2c1e5dd"], "color": ""}, "nodeID": "66c5f9e7ac196950e2c1e5df", "coords": [5209.315155894732, -1239.5418355515142]}, "66c5fa29ac196950e2c1e5ea": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5fa29ac196950e2c1e5ea"}, "66c5fa29ac196950e2c1e5eb": {"type": "actions", "data": {"name": "", "steps": ["66c5fa29ac196950e2c1e5ea"]}, "nodeID": "66c5fa29ac196950e2c1e5eb", "coords": [0, 0]}, "66c5fa3cac196950e2c1e5f0": {"type": "url", "data": {"name": "", "url": "{{[url].66be69cd0d3bb525af0538ee}}", "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66c5a7be2825ec011ad625a4", "id": "66c5fa3cac196950e2c1e5f2", "data": {"points": [{"point": [4711.39, -894.06]}, {"point": [4888.41, -894.06]}, {"point": [4888.41, -941.91]}, {"point": [5065.44, -941.91], "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "66c5fa3cac196950e2c1e5f0"}, "66c5fa3cac196950e2c1e5f1": {"type": "actions", "data": {"name": "", "steps": ["66c5fa3cac196950e2c1e5f0"]}, "nodeID": "66c5fa3cac196950e2c1e5f1", "coords": [0, 0]}, "66c5fac1ac196950e2c1e5fa": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "cri33fg3", "name": "Try again", "actions": []}, {"id": "ctil3fg6", "name": "Cancel", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66c5fac1ac196950e2c1e5fc"}}, "dynamic": [{"type": "", "target": "66c5fb1dac196950e2c1e603", "id": "66c5fac1ac196950e2c1e5fb", "data": {}}, {"id": "66c5facaac196950e2c1e5ff", "type": "", "target": "66c5fe1aac196950e2c1e60c", "data": {}}]}}, "nodeID": "66c5fac1ac196950e2c1e5fa", "coords": [0, 0]}, "66c5fb1dac196950e2c1e602": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5f472b9205a485a70e969", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5fb1dac196950e2c1e602"}, "66c5fb1dac196950e2c1e603": {"type": "actions", "data": {"name": "", "steps": ["66c5fb1dac196950e2c1e602"]}, "nodeID": "66c5fb1dac196950e2c1e603", "coords": [0, 0]}, "66c5fe1aac196950e2c1e60b": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5f9e7ac196950e2c1e5df", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5fe1aac196950e2c1e60b"}, "66c5fe1aac196950e2c1e60c": {"type": "actions", "data": {"name": "", "steps": ["66c5fe1aac196950e2c1e60b"]}, "nodeID": "66c5fe1aac196950e2c1e60c", "coords": [0, 0]}, "66cf35ee2b174507e0a525c5": {"type": "message", "data": {"name": "", "messageID": "66cf35f093364975386e4223", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66cf35ee2b174507e0a525c6"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66cf35ee2b174507e0a525c5"}, "66cf36562b174507e0a525c9": {"type": "message", "data": {"name": "", "messageID": "66cf365893364975386e425f", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5f472b9205a485a70e966", "id": "66cf36562b174507e0a525ca", "data": {"points": [{"point": [4714.1, -712.64]}, {"point": [4790.99, -712.64]}, {"point": [4790.99, -653.32]}, {"point": [4867.89, -653.32], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66cf36562b174507e0a525c9"}, "66cf37cf2b174507e0a525d6": {"type": "block", "data": {"name": "Display stripe checkout url", "steps": ["66da130f44e67d133b44d977", "66c5f992ac196950e2c1e5d0"], "color": ""}, "nodeID": "66cf37cf2b174507e0a525d6", "coords": [4555.992687989675, -1435.645828316284]}, "66d2e993715631612c264620": {"type": "code", "data": {"name": "", "code": "function generateDateOptions() {\r\n    const currentDate = new Date();\r\n    const maxDate = new Date();\r\n    maxDate.setMonth(currentDate.getMonth() + 3);\r\n\r\n    const dateOptions = [];\r\n\r\n    while (currentDate <= maxDate) {\r\n        const day = currentDate.getDate();\r\n        const month = currentDate.toLocaleString('default', { month: 'short' });\r\n        dateOptions.push(`${month} ${day}`);\r\n\r\n        // Move to the next day\r\n        currentDate.setDate(currentDate.getDate() + 1);\r\n    }\r\n\r\n    return dateOptions;\r\n}\r\n\r\n\r\n\r\n// Paginate Dates\r\nfunction paginateDates(datesArray, page = 1, itemsPerPage = 10) {\r\n    // Calculate the start and end indices for slicing the array\r\n    const startIndex = (page - 1) * itemsPerPage;\r\n    const endIndex = startIndex + itemsPerPage;\r\n\r\n    // Slice the array to get the current page's dates\r\n    const currentDates = datesArray.slice(startIndex, endIndex);\r\n\r\n    // Check if there's a next page\r\n    const hasMoreDates = endIndex < datesArray.length;\r\n\r\n    return {\r\n        dates: currentDates,\r\n        hasMore: hasMoreDates,\r\n        nextPage: hasMoreDates ? page + 1 : null\r\n    };\r\n}\r\n\r\n\r\nlet availableDates = generateDateOptions(); // Assume this function returns the full array of dates\r\nlet currentPage = 1;\r\n\r\n\r\nfunction displayDates() {\r\n    const result = paginateDates(availableDates, currentPage);\r\n\r\n    console.log(\"Available dates to pick from:\", result.dates);\r\n\r\n    let [date1, date2, date3, date4, date5, date6, date7, date8, date9, date10] = result.dates;\r\n\r\n    \r\n    \r\n    firstDate = date1;\r\n    secondDate = date2;\r\n    thirdDate = date3;\r\n    fourthDate = date4;\r\n    fifthDate = date5;\r\n    sixthDate = date6;\r\n    seventhDate = date7;\r\n    eighthDate = date8;\r\n    ninthDate = date9;\r\n    tenthDate = date10;\r\n    \r\n\r\n    if (result.hasMore) {\r\n        console.log(\"Click 'Show More' to see more dates.\");\r\n        currentPage = result.nextPage; // Update to the next page for the next call\r\n    } else {\r\n        console.log(\"No more dates available.\");\r\n    }\r\n}\r\n\r\n// showMore = \"None\";\r\ndisplayDates();\r\n\r\n\r\n// Function to trigger pagination when 'showMore' is set to 'next'\r\nfunction checkShowMore() {\r\n    if (showMore === \"next\") {\r\n        displayDates();\r\n    }\r\n}\r\n\r\n// Simulate the user clicking \"next\"\r\n// showMore = \"None\";\r\ncheckShowMore(); // This will call displayDates() again to show the next 10 dates", "paths": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d2ebea715631612c26462c", "id": "66d2e993715631612c264621", "data": {"points": [{"point": [713.18, -2444.22]}, {"point": [737.18, -2444.22]}, {"point": [737.18, -2313.51]}, {"point": [658.52, -2313.51]}, {"point": [658.52, -2182.79], "toTop": true, "allowedToTop": true}]}}, "fail": {"type": "fail", "target": null, "id": "66d2e993715631612c264622"}}, "dynamic": []}}, "nodeID": "66d2e993715631612c264620"}, "66d2e993715631612c264623": {"type": "block", "data": {"name": "Select date", "steps": ["66d2e993715631612c264620"], "color": ""}, "nodeID": "66d2e993715631612c264623", "coords": [546.9990840871333, -2577.7670887802688]}, "66d2ebea715631612c26462a": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d2ec14715631612c264633", "id": "66d2ebea715631612c26462b", "data": {"points": [{"point": [824.7, -2083.62]}, {"point": [848.7, -2083.62]}, {"point": [848.7, -2701.18]}, {"point": [670.86, -2701.18]}, {"point": [670.86, -3318.73]}, {"point": [694.86, -3318.73], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}, "messageID": "66d2ebf67f0b84ecf04fc94a"}, "nodeID": "66d2ebea715631612c26462a"}, "66d2ebea715631612c26462c": {"type": "block", "data": {"name": "New Block 26", "steps": ["66d2ebea715631612c26462a"]}, "nodeID": "66d2ebea715631612c26462c", "coords": [433.8550689670415, -2248.54703416474]}, "66d2ec14715631612c264633": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "btet3aeq", "name": "{{[firstDate].66d2ec617f0b84ecf04fc97f}}", "actions": []}, {"id": "xsf63ar8", "name": "{{[secondDate].66d2ec6d7f0b84ecf04fc980}}", "actions": []}, {"id": "2ifi3abb", "name": "{{[thirdDate].66d2ec777f0b84ecf04fc981}}", "actions": []}, {"id": "pefs3axl", "name": "{{[fourthDate].66d2ec7f7f0b84ecf04fc982}}", "actions": []}, {"id": "j5g03agl", "name": "{{[fifthDate].66d2ec8b7f0b84ecf04fc985}}", "actions": []}, {"id": "1yg73ab4", "name": "{{[sixthDate].66d2ec997f0b84ecf04fc991}}", "actions": [], "intent": null}, {"id": "gcge3aag", "name": "{{[seventhDate].66d2eca47f0b84ecf04fc9a6}}", "actions": []}, {"id": "pogn3awy", "name": "{{[eighthDate].66d2ecbb7f0b84ecf04fc9a9}}", "actions": []}, {"id": "77h53ajm", "name": "{{[ninthDate].66d2ed067f0b84ecf04fc9ba}}", "actions": []}, {"id": "6jhd3at8", "name": "{{[tenthDate].66d2ed117f0b84ecf04fc9bd}}", "actions": []}, {"id": "upfo3eob", "name": "Show more", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66d2ec14715631612c264635"}}, "dynamic": [{"type": "", "target": "66d5a7c2c9528810f90a4121", "id": "66d2ec14715631612c264634", "data": {}}, {"id": "66d2ec17715631612c264638", "type": "", "target": "66d5a7d9c9528810f90a4127", "data": {}}, {"id": "66d2ec1a715631612c26463b", "type": "", "target": "66d5a7edc9528810f90a412d", "data": {}}, {"id": "66d2ec1c715631612c26463d", "type": "", "target": "66d5a801c9528810f90a4133", "data": {}}, {"id": "66d2ec1d715631612c26463f", "type": "", "target": "66d5a813c9528810f90a4139", "data": {}}, {"id": "66d2ec1e715631612c264641", "type": "", "target": "66d5a82bc9528810f90a413f", "data": {}}, {"id": "66d2ec1e715631612c264643", "type": "", "target": "66d5a844c9528810f90a4145", "data": {}}, {"id": "66d2ec20715631612c264645", "type": "", "target": "66d5a857c9528810f90a414b", "data": {}}, {"id": "66d2ec26715631612c264647", "type": "", "target": "66d5a872c9528810f90a4151", "data": {}}, {"id": "66d2ec27715631612c264649", "type": "", "target": "66d5a88bc9528810f90a4157", "data": {}}, {"id": "66d2f66aa29a5cc191436fd3", "type": "", "target": "66d5a2ffc9528810f90a40d7", "data": {"points": [{"point": [694.86, -2778.32], "reversed": true}, {"point": [422.16, -2778.32]}, {"point": [422.16, -1654.66]}, {"point": [149.47, -1654.66], "reversed": true, "allowedToTop": false}]}}]}}, "nodeID": "66d2ec14715631612c264633", "coords": [0, 0]}, "66d5a273c9528810f90a4035": {"type": "block", "data": {"name": "Generate Dates II", "color": "", "steps": ["66d5a273c9528810f90a4037"]}, "nodeID": "66d5a273c9528810f90a4035", "coords": [-688.973066575768, -1738.9361503347152]}, "66d5a273c9528810f90a4037": {"type": "code", "data": {"name": "", "code": "function generateDateOptions() {\r\n    const currentDate = new Date();\r\n    const maxDate = new Date();\r\n    maxDate.setMonth(currentDate.getMonth() + 3);\r\n\r\n    const dateOptions = [];\r\n\r\n    while (currentDate <= maxDate) {\r\n        const day = currentDate.getDate();\r\n        const month = currentDate.toLocaleString('default', { month: 'short' });\r\n        dateOptions.push(`${month} ${day}`);\r\n\r\n        // Move to the next day\r\n        currentDate.setDate(currentDate.getDate() + 1);\r\n    }\r\n\r\n    return dateOptions;\r\n}\r\n\r\n\r\n\r\n// Paginate Dates\r\nfunction paginateDates(datesArray, page = 1, itemsPerPage = 10) {\r\n    // Calculate the start and end indices for slicing the array\r\n    const startIndex = (page - 1) * itemsPerPage;\r\n    const endIndex = startIndex + itemsPerPage;\r\n\r\n    // Slice the array to get the current page's dates\r\n    const currentDates = datesArray.slice(startIndex, endIndex);\r\n\r\n    // Check if there's a next page\r\n    const hasMoreDates = endIndex < datesArray.length;\r\n\r\n    return {\r\n        dates: currentDates,\r\n        hasMore: hasMoreDates,\r\n        nextPage: hasMoreDates ? page + 1 : null\r\n    };\r\n}\r\n\r\n\r\nlet availableDates = generateDateOptions(); // Assume this function returns the full array of dates\r\nlet currentPage = 1;\r\n\r\n\r\nfunction displayDates() {\r\n    const result = paginateDates(availableDates, currentPage);\r\n\r\n    console.log(\"Available dates to pick from:\", result.dates);\r\n\r\n    let [date1, date2, date3, date4, date5, date6, date7, date8, date9, date10] = result.dates;\r\n\r\n    \r\n    \r\n    firstDate = date1;\r\n    secondDate = date2;\r\n    thirdDate = date3;\r\n    fourthDate = date4;\r\n    fifthDate = date5;\r\n    sixthDate = date6;\r\n    seventhDate = date7;\r\n    eighthDate = date8;\r\n    ninthDate = date9;\r\n    tenthDate = date10;\r\n    \r\n\r\n    if (result.hasMore) {\r\n        console.log(\"Click 'Show More' to see more dates.\");\r\n        currentPage = result.nextPage; // Update to the next page for the next call\r\n    } else {\r\n        console.log(\"No more dates available.\");\r\n    }\r\n}\r\n\r\n// showMore = \"None\";\r\ndisplayDates();\r\n\r\n\r\n// Function to trigger pagination when 'showMore' is set to 'next'\r\nfunction checkShowMore() {\r\n    if (showMore === \"next\") {\r\n        displayDates();\r\n    }\r\n}\r\n\r\n// Simulate the user clicking \"next\"\r\n// showMore = \"None\";\r\ncheckShowMore(); // This will call displayDates() again to show the next 10 dates", "paths": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d5a2bfc9528810f90a409d", "id": "66d5a273c9528810f90a4039", "data": {"points": [{"point": [-522.79, -1605.39]}, {"point": [-498.79, -1605.39]}, {"point": [-498.79, -2015.34]}, {"point": [-592.96, -2015.34]}, {"point": [-592.96, -2425.29]}, {"point": [-568.96, -2425.29], "allowedToTop": false}]}}, "fail": {"type": "fail", "target": null, "id": "66d5a273c9528810f90a4038"}}, "dynamic": []}}, "nodeID": "66d5a273c9528810f90a4037"}, "66d5a2bfc9528810f90a409b": {"type": "block", "data": {"name": "New Block 29", "color": "", "steps": ["66d5a2bfc9528810f90a409d"]}, "nodeID": "66d5a2bfc9528810f90a409b", "coords": [-402.7818954904636, -2504.1116383722415]}, "66d5a2bfc9528810f90a409d": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "btet3aeq", "name": "{{[firstDate].66d2ec617f0b84ecf04fc97f}}", "actions": []}, {"id": "xsf63ar8", "name": "{{[secondDate].66d2ec6d7f0b84ecf04fc980}}", "actions": []}, {"id": "2ifi3abb", "name": "{{[thirdDate].66d2ec777f0b84ecf04fc981}}", "actions": []}, {"id": "pefs3axl", "name": "{{[fourthDate].66d2ec7f7f0b84ecf04fc982}}", "actions": []}, {"id": "j5g03agl", "name": "{{[fifthDate].66d2ec8b7f0b84ecf04fc985}}", "actions": [], "intent": null}, {"id": "1yg73ab4", "name": "{{[sixthDate].66d2ec997f0b84ecf04fc991}}", "actions": []}, {"id": "gcge3aag", "name": "{{[seventhDate].66d2eca47f0b84ecf04fc9a6}}", "actions": []}, {"id": "pogn3awy", "name": "{{[eighthDate].66d2ecbb7f0b84ecf04fc9a9}}", "actions": []}, {"id": "77h53ajm", "name": "{{[ninthDate].66d2ed067f0b84ecf04fc9ba}}", "actions": []}, {"id": "6jhd3at8", "name": "{{[tenthDate].66d2ed117f0b84ecf04fc9bd}}", "actions": []}, {"id": "upfo3eob", "name": "", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66d5a2bfc9528810f90a409e"}}, "dynamic": [{"type": "", "target": "66d5a6cac9528810f90a40e5", "id": "66d5a2bfc9528810f90a409f", "data": {}}, {"type": "", "target": "66d5a6f6c9528810f90a40eb", "id": "66d5a2bfc9528810f90a40a0", "data": {}}, {"type": "", "target": "66d5a707c9528810f90a40f1", "id": "66d5a2bfc9528810f90a40a1", "data": {}}, {"type": "", "target": "66d5a715c9528810f90a40f7", "id": "66d5a2bfc9528810f90a40a2", "data": {}}, {"type": "", "target": "66d5a72bc9528810f90a40fd", "id": "66d5a2bfc9528810f90a40a3", "data": {}}, {"type": "", "target": "66d5a73ec9528810f90a4103", "id": "66d5a2bfc9528810f90a40a4", "data": {}}, {"type": "", "target": "66d5a751c9528810f90a4109", "id": "66d5a2bfc9528810f90a40a5", "data": {}}, {"type": "", "target": "66d5a766c9528810f90a410f", "id": "66d5a2bfc9528810f90a40a6", "data": {}}, {"type": "", "target": "66d5a77ec9528810f90a4115", "id": "66d5a2bfc9528810f90a40a7", "data": {}}, {"type": "", "target": "66d5a795c9528810f90a411b", "id": "66d5a2bfc9528810f90a40a8", "data": {}}, {"type": "", "target": null, "id": "66d5a2bfc9528810f90a40a9", "data": {"points": [{"point": [-349.58000000000004, 626.31]}, {"point": [-311.03, 626.31]}, {"point": [-311.03, 643.31]}, {"point": [-272.47, 643.31], "allowedToTop": true}]}}]}}, "nodeID": "66d5a2bfc9528810f90a409d", "coords": [0, 0]}, "66d5a2ffc9528810f90a40d7": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5a273c9528810f90a4037", "id": "66d5a2ffc9528810f90a40d8", "data": {"points": [{"point": [-182.9, -1654.42], "reversed": true}, {"point": [-522.79, -1660.12], "reversed": true}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p7409907nx298bhlo0", "label": null, "variableID": "66d2f7957f0b84ecf04fcf06", "type": "script", "value": ["\"next\""]}]}, "nodeID": "66d5a2ffc9528810f90a40d7"}, "66d5a2ffc9528810f90a40d9": {"type": "block", "data": {"name": "New Block 29", "steps": ["66d5a2ffc9528810f90a40d7"]}, "nodeID": "66d5a2ffc9528810f90a40d9", "coords": [-16.716161655800377, -1733.4792844053393]}, "66d5a6cac9528810f90a40e4": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a6cac9528810f90a40e4"}, "66d5a6cac9528810f90a40e5": {"type": "actions", "data": {"name": "", "steps": ["678d9f59c2e29027e4e5ec6e", "66d5a6cac9528810f90a40e4"]}, "nodeID": "66d5a6cac9528810f90a40e5", "coords": [0, 0]}, "66d5a6f6c9528810f90a40ea": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a6f6c9528810f90a40ea"}, "66d5a6f6c9528810f90a40eb": {"type": "actions", "data": {"name": "", "steps": ["678d9facc2e29027e4e5ec71", "66d5a6f6c9528810f90a40ea"]}, "nodeID": "66d5a6f6c9528810f90a40eb", "coords": [0, 0]}, "66d5a707c9528810f90a40f0": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a707c9528810f90a40f0"}, "66d5a707c9528810f90a40f1": {"type": "actions", "data": {"name": "", "steps": ["678d9fd3c2e29027e4e5ec74", "66d5a707c9528810f90a40f0"]}, "nodeID": "66d5a707c9528810f90a40f1", "coords": [0, 0]}, "66d5a715c9528810f90a40f6": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a715c9528810f90a40f6"}, "66d5a715c9528810f90a40f7": {"type": "actions", "data": {"name": "", "steps": ["678d9ff1c2e29027e4e5ec77", "66d5a715c9528810f90a40f6"]}, "nodeID": "66d5a715c9528810f90a40f7", "coords": [0, 0]}, "66d5a72bc9528810f90a40fc": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a72bc9528810f90a40fc"}, "66d5a72bc9528810f90a40fd": {"type": "actions", "data": {"name": "", "steps": ["678da01dc2e29027e4e5ec7a", "66d5a72bc9528810f90a40fc"]}, "nodeID": "66d5a72bc9528810f90a40fd", "coords": [0, 0]}, "66d5a73ec9528810f90a4102": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a73ec9528810f90a4102"}, "66d5a73ec9528810f90a4103": {"type": "actions", "data": {"name": "", "steps": ["678da043c2e29027e4e5ec7d", "66d5a73ec9528810f90a4102"]}, "nodeID": "66d5a73ec9528810f90a4103", "coords": [0, 0]}, "66d5a751c9528810f90a4108": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a751c9528810f90a4108"}, "66d5a751c9528810f90a4109": {"type": "actions", "data": {"name": "", "steps": ["678da061c2e29027e4e5ec80", "66d5a751c9528810f90a4108"]}, "nodeID": "66d5a751c9528810f90a4109", "coords": [0, 0]}, "66d5a766c9528810f90a410e": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a766c9528810f90a410e"}, "66d5a766c9528810f90a410f": {"type": "actions", "data": {"name": "", "steps": ["678da095c2e29027e4e5ec83", "66d5a766c9528810f90a410e"]}, "nodeID": "66d5a766c9528810f90a410f", "coords": [0, 0]}, "66d5a77ec9528810f90a4114": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a77ec9528810f90a4114"}, "66d5a77ec9528810f90a4115": {"type": "actions", "data": {"name": "", "steps": ["678da0b5c2e29027e4e5ec86", "66d5a77ec9528810f90a4114"]}, "nodeID": "66d5a77ec9528810f90a4115", "coords": [0, 0]}, "66d5a795c9528810f90a411a": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a795c9528810f90a411a"}, "66d5a795c9528810f90a411b": {"type": "actions", "data": {"name": "", "steps": ["678da0d4c2e29027e4e5ec89", "66d5a795c9528810f90a411a"]}, "nodeID": "66d5a795c9528810f90a411b", "coords": [0, 0]}, "66d5a7c2c9528810f90a4120": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a7c2c9528810f90a4120"}, "66d5a7c2c9528810f90a4121": {"type": "actions", "data": {"name": "", "steps": ["678e1fff32bdf36a07744628", "66d5a7c2c9528810f90a4120"]}, "nodeID": "66d5a7c2c9528810f90a4121", "coords": [0, 0]}, "66d5a7d9c9528810f90a4126": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a7d9c9528810f90a4126"}, "66d5a7d9c9528810f90a4127": {"type": "actions", "data": {"name": "", "steps": ["678e202d32bdf36a0774462b", "66d5a7d9c9528810f90a4126"]}, "nodeID": "66d5a7d9c9528810f90a4127", "coords": [0, 0]}, "66d5a7edc9528810f90a412c": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a7edc9528810f90a412c"}, "66d5a7edc9528810f90a412d": {"type": "actions", "data": {"name": "", "steps": ["678e204632bdf36a0774462e", "66d5a7edc9528810f90a412c"]}, "nodeID": "66d5a7edc9528810f90a412d", "coords": [0, 0]}, "66d5a801c9528810f90a4132": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a801c9528810f90a4132"}, "66d5a801c9528810f90a4133": {"type": "actions", "data": {"name": "", "steps": ["678e206232bdf36a07744631", "66d5a801c9528810f90a4132"]}, "nodeID": "66d5a801c9528810f90a4133", "coords": [0, 0]}, "66d5a813c9528810f90a4138": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a813c9528810f90a4138"}, "66d5a813c9528810f90a4139": {"type": "actions", "data": {"name": "", "steps": ["678e208032bdf36a07744634", "66d5a813c9528810f90a4138"]}, "nodeID": "66d5a813c9528810f90a4139", "coords": [0, 0]}, "66d5a82bc9528810f90a413e": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a82bc9528810f90a413e"}, "66d5a82bc9528810f90a413f": {"type": "actions", "data": {"name": "", "steps": ["678e209f32bdf36a07744637", "66d5a82bc9528810f90a413e"]}, "nodeID": "66d5a82bc9528810f90a413f", "coords": [0, 0]}, "66d5a844c9528810f90a4144": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a844c9528810f90a4144"}, "66d5a844c9528810f90a4145": {"type": "actions", "data": {"name": "", "steps": ["678e20d732bdf36a0774463a", "66d5a844c9528810f90a4144"]}, "nodeID": "66d5a844c9528810f90a4145", "coords": [0, 0]}, "66d5a857c9528810f90a414a": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a857c9528810f90a414a"}, "66d5a857c9528810f90a414b": {"type": "actions", "data": {"name": "", "steps": ["678e20f032bdf36a0774463d", "66d5a857c9528810f90a414a"]}, "nodeID": "66d5a857c9528810f90a414b", "coords": [0, 0]}, "66d5a872c9528810f90a4150": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a872c9528810f90a4150"}, "66d5a872c9528810f90a4151": {"type": "actions", "data": {"name": "", "steps": ["678e211232bdf36a07744640", "66d5a872c9528810f90a4150"]}, "nodeID": "66d5a872c9528810f90a4151", "coords": [0, 0]}, "66d5a88bc9528810f90a4156": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5a88bc9528810f90a4156"}, "66d5a88bc9528810f90a4157": {"type": "actions", "data": {"name": "", "steps": ["678e212d32bdf36a07744643", "66d5a88bc9528810f90a4156"]}, "nodeID": "66d5a88bc9528810f90a4157", "coords": [0, 0]}, "66c59f5c2825ec011ad62406": {"type": "goToNode", "data": {"name": "", "nodeID": "66d2e993715631612c264623", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c59f5c2825ec011ad62406"}, "66c59f5c2825ec011ad62407": {"type": "actions", "data": {"name": "", "steps": ["66d5ae15c0eb025abfab4fb2", "66c59f5c2825ec011ad62406"]}, "nodeID": "66c59f5c2825ec011ad62407", "coords": [0, 0]}, "66d5ae15c0eb025abfab4fb2": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5ae15c0eb025abfab4fb3"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p7409a07nx4iym4jqb", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5ae15c0eb025abfab4fb2"}, "66d5af27c0eb025abfab4fb7": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5af27c0eb025abfab4fb8"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p7409b07nx89ujars4", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5af27c0eb025abfab4fb7"}, "66d5af55c0eb025abfab4fba": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5af55c0eb025abfab4fbb"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p7409c07nxew0dcvkl", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5af55c0eb025abfab4fba"}, "66d5af78c0eb025abfab4fbd": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5af78c0eb025abfab4fbe"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409d07nxaxvb1dry", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5af78c0eb025abfab4fbd"}, "66d5af9ac0eb025abfab4fc0": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5af9ac0eb025abfab4fc1"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409e07nxfsm4b3gs", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5af9ac0eb025abfab4fc0"}, "66d5afc3c0eb025abfab4fc3": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5afc3c0eb025abfab4fc4"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409f07nx7b31a3hz", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5afc3c0eb025abfab4fc3"}, "66d5afdec0eb025abfab4fc6": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5afdec0eb025abfab4fc7"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409g07nxh42pgm51", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5afdec0eb025abfab4fc6"}, "66d5affac0eb025abfab4fc9": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5affac0eb025abfab4fca"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409h07nxffw72orq", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5affac0eb025abfab4fc9"}, "66d5b024c0eb025abfab4fcc": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b024c0eb025abfab4fcd"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409i07nx1m18f75d", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b024c0eb025abfab4fcc"}, "66d5b049c0eb025abfab4fcf": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b049c0eb025abfab4fd0"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409j07nx5p0ndi37", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b049c0eb025abfab4fcf"}, "66d5b075c0eb025abfab4fd2": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b075c0eb025abfab4fd3"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409k07nxcilahw2r", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b075c0eb025abfab4fd2"}, "66d5b098c0eb025abfab4fd5": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b098c0eb025abfab4fd6"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p8409l07nx7g9a5y2s", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b098c0eb025abfab4fd5"}, "66d5b0c5c0eb025abfab4fd8": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b0c5c0eb025abfab4fd9"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409m07nxc5zefors", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b0c5c0eb025abfab4fd8"}, "66d5b0e0c0eb025abfab4fdb": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b0e0c0eb025abfab4fdc"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409n07nx52rug9k6", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b0e0c0eb025abfab4fdb"}, "66d5b0fac0eb025abfab4fde": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b0fac0eb025abfab4fdf"}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409o07nx30v938g5", "label": null, "variableID": "66d5adc67f0b84ecf050faf0", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5b0fac0eb025abfab4fde"}, "66d5b1fac0eb025abfab4fe1": {"type": "ifV2", "data": {"name": "If", "expressions": [{"type": "and", "name": "Check party size less than or equal to 2.", "value": [{"id": "7z1rs3ayc", "logicInterface": "logic_group", "type": "and", "value": [{"logicInterface": "variable", "type": "less_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "2"}]}, {"logicInterface": "variable", "type": "less_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "2"}]}]}]}, {"type": "and", "name": "Check party size of 3 - 10", "value": [{"id": "bc1px3ac7", "logicInterface": "logic_group", "type": "and", "value": [{"logicInterface": "variable", "type": "greater_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "3"}]}, {"logicInterface": "variable", "type": "less_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "10"}]}]}]}, {"type": null, "name": "Check party size 11 -15", "value": [{"id": "q61wd3ap9", "logicInterface": "logic_group", "type": "and", "value": [{"logicInterface": "variable", "type": "greater_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "11"}]}, {"logicInterface": "variable", "type": "less_or_equal", "value": [{"type": "variable", "value": "66d5adc67f0b84ecf050faf0"}, {"type": "value", "value": "15"}]}]}]}], "noMatch": {"type": "none", "pathName": "No match"}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66d5b1fac0eb025abfab4fe3"}}, "dynamic": [{"type": "", "target": "66d5b613c0eb025abfab4ff4", "id": "66d5b1fac0eb025abfab4fe2", "data": {}}, {"id": "66d5b23ec0eb025abfab4fea", "type": "", "target": "66d5b915c0eb025abfab4ffc", "data": {}}, {"id": "66d5b531c0eb025abfab4fec", "type": "", "target": "66d5b981c0eb025abfab5004", "data": {}}]}}, "nodeID": "66d5b1fac0eb025abfab4fe1"}, "66d5b1fac0eb025abfab4fe4": {"type": "block", "data": {"name": "Calculate Deposit Fee", "steps": ["66d5b1fac0eb025abfab4fe1"], "color": ""}, "nodeID": "66d5b1fac0eb025abfab4fe4", "coords": [3122.276933424232, -448.93615033471497]}, "66d5b613c0eb025abfab4ff3": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b613c0eb025abfab4ff5", "data": {}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409p07nxgdn85scz", "label": null, "variableID": "66d5b6307f0b84ecf050ff38", "type": "script", "value": ["\"£50.00\""]}]}, "nodeID": "66d5b613c0eb025abfab4ff3"}, "66d5b613c0eb025abfab4ff4": {"type": "actions", "data": {"name": "", "steps": ["66d5b613c0eb025abfab4ff3", "66d5c3e0c0eb025abfab5016"]}, "nodeID": "66d5b613c0eb025abfab4ff4", "coords": [0, 0]}, "66d5b915c0eb025abfab4ffb": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b915c0eb025abfab4ffd", "data": {"points": [{"point": [4050.21, -314.85]}, {"point": [4074.21, -314.85]}, {"point": [4074.21, -825.66]}, {"point": [3999.87, -825.66], "reversed": true, "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409q07nx9g0x0zu8", "label": null, "variableID": "66d5b6307f0b84ecf050ff38", "type": "script", "value": ["\"£100.00\""]}]}, "nodeID": "66d5b915c0eb025abfab4ffb"}, "66d5b915c0eb025abfab4ffc": {"type": "actions", "data": {"name": "", "steps": ["66d5b915c0eb025abfab4ffb", "66d5c49bc0eb025abfab501b"]}, "nodeID": "66d5b915c0eb025abfab4ffc", "coords": [0, 0]}, "66d5b981c0eb025abfab5003": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5b981c0eb025abfab5005", "data": {"points": [{"point": [4050.21, -260.37]}, {"point": [4074.21, -260.37]}, {"point": [4074.21, -876.77]}, {"point": [3997.98, -876.77], "reversed": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409r07nxerhecswi", "label": null, "variableID": "66d5b6307f0b84ecf050ff38", "type": "script", "value": ["\"£300.00\""]}]}, "nodeID": "66d5b981c0eb025abfab5003"}, "66d5b981c0eb025abfab5004": {"type": "actions", "data": {"name": "", "steps": ["66d5b981c0eb025abfab5003", "66d5c4bac0eb025abfab501f"]}, "nodeID": "66d5b981c0eb025abfab5004", "coords": [0, 0]}, "66d5c3e0c0eb025abfab5016": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a4d82825ec011ad62503", "id": "66d5c3e0c0eb025abfab5017", "data": {}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54p9409s07nxby9lbnxc", "label": null, "variableID": "66d5c41e7f0b84ecf05108eb", "type": "script", "value": ["5000"]}]}, "nodeID": "66d5c3e0c0eb025abfab5016"}, "66d5c49bc0eb025abfab501b": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a4d82825ec011ad62503", "id": "66d5c49bc0eb025abfab501c", "data": {}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409t07nxdm4q1kgl", "label": null, "variableID": "66d5c41e7f0b84ecf05108eb", "type": "script", "value": ["10000"]}]}, "nodeID": "66d5c49bc0eb025abfab501b"}, "66d5c4bac0eb025abfab501f": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5a4d82825ec011ad62503", "id": "66d5c4bac0eb025abfab5020", "data": {}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409u07nx754p56wx", "label": null, "variableID": "66d5c41e7f0b84ecf05108eb", "type": "script", "value": ["30000"]}]}, "nodeID": "66d5c4bac0eb025abfab501f"}, "66d5ccbe176e7b205fc4c2ee": {"type": "message", "data": {"name": "", "messageID": "66d5ccc17f0b84ecf05110ea", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5ccbe176e7b205fc4c2ef"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5ccbe176e7b205fc4c2ee"}, "66d5ccbe176e7b205fc4c2f0": {"type": "block", "data": {"name": "Get user email address", "steps": ["66d5ccbe176e7b205fc4c2ee", "66d5ccd9176e7b205fc4c2f6"], "color": ""}, "nodeID": "66d5ccbe176e7b205fc4c2f0", "coords": [3330.194001971575, -1871.319245101384]}, "66d5ccd9176e7b205fc4c2f6": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d5cead176e7b205fc4c32d", "id": "66d5ccd9176e7b205fc4c2f7", "data": {}}, "else": {"type": "else", "target": null, "id": "66d5ccd9176e7b205fc4c2f8"}}, "dynamic": []}}, "nodeID": "66d5ccd9176e7b205fc4c2f6"}, "66d5cd11176e7b205fc4c2fd": {"type": "block", "data": {"name": "Get user last name", "color": "", "steps": ["66d5cd11176e7b205fc4c2ff", "66d5cd11176e7b205fc4c302"]}, "nodeID": "66d5cd11176e7b205fc4c2fd", "coords": [3360.123264411367, -1202.51221107265]}, "66d5cd11176e7b205fc4c2ff": {"type": "message", "data": {"name": "", "messageID": "66d5cd12f9fabfe28b707605", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5cd11176e7b205fc4c300"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5cd11176e7b205fc4c2ff"}, "66d5cd11176e7b205fc4c302": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d5cef4176e7b205fc4c335", "id": "66d5cd11176e7b205fc4c303", "data": {}}, "else": {"type": "else", "target": null, "id": "66d5cd11176e7b205fc4c304"}}, "dynamic": []}}, "nodeID": "66d5cd11176e7b205fc4c302"}, "66d5cde7176e7b205fc4c30d": {"type": "block", "data": {"name": "Get user first name", "color": "", "steps": ["66d5cde7176e7b205fc4c30f", "66d5cde7176e7b205fc4c312"]}, "nodeID": "66d5cde7176e7b205fc4c30d", "coords": [333.00280818005416, -1245.689749252912]}, "66d5cde7176e7b205fc4c30f": {"type": "message", "data": {"name": "", "messageID": "66d5cde7f9fabfe28b707623", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5cde7176e7b205fc4c310"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5cde7176e7b205fc4c30f"}, "66d5cde7176e7b205fc4c312": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d5cf50176e7b205fc4c33d", "id": "66d5cde7176e7b205fc4c313", "data": {}}, "else": {"type": "else", "target": null, "id": "66d5cde7176e7b205fc4c314"}}, "dynamic": []}}, "nodeID": "66d5cde7176e7b205fc4c312", "coords": [0, 0]}, "66d5ce3f176e7b205fc4c31d": {"type": "block", "data": {"name": "Get user mobile number", "color": "", "steps": ["66d5ce3f176e7b205fc4c31f", "66d5ce3f176e7b205fc4c322"]}, "nodeID": "66d5ce3f176e7b205fc4c31d", "coords": [3330.412168879849, -1527.6771592964235]}, "66d5ce3f176e7b205fc4c31f": {"type": "message", "data": {"name": "", "messageID": "66d5ce40f9fabfe28b707627", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66d5ce3f176e7b205fc4c320"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66d5ce3f176e7b205fc4c31f"}, "66d5ce3f176e7b205fc4c322": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66d5d413176e7b205fc4c373", "id": "66d5ce3f176e7b205fc4c323", "data": {}}, "else": {"type": "else", "target": null, "id": "66d5ce3f176e7b205fc4c324"}}, "dynamic": []}}, "nodeID": "66d5ce3f176e7b205fc4c322", "coords": [0, 0]}, "66d5cead176e7b205fc4c32c": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66db03772621cc581a4c0e40", "id": "66d5cead176e7b205fc4c32e", "data": {"points": [{"point": [3771.22, -1686.24]}, {"point": [3894.64, -1686.24]}, {"point": [3894.64, -1327.98], "toTop": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409v07nxapyuggak", "label": null, "variableID": "66d5cfa37f0b84ecf05112b1", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5cead176e7b205fc4c32c"}, "66d5cead176e7b205fc4c32d": {"type": "actions", "data": {"name": "", "steps": ["66d5cead176e7b205fc4c32c"]}, "nodeID": "66d5cead176e7b205fc4c32d", "coords": [0, 0]}, "66d5cef4176e7b205fc4c334": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5ce3f176e7b205fc4c31d", "id": "66d5cef4176e7b205fc4c336", "data": {"points": [{"point": [2919.1, -1037.43], "reversed": true}, {"point": [2895.1, -1037.43]}, {"point": [2895.1, -1500.68]}, {"point": [3165.41, -1500.68], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409w07nxhbmagft6", "label": null, "variableID": "66d5cf1d7f0b84ecf051124f", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5cef4176e7b205fc4c334"}, "66d5cef4176e7b205fc4c335": {"type": "actions", "data": {"name": "", "steps": ["66d5cef4176e7b205fc4c334"]}, "nodeID": "66d5cef4176e7b205fc4c335", "coords": [0, 0]}, "66d5cf50176e7b205fc4c33c": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c59dd22825ec011ad6238a", "id": "66d5cf50176e7b205fc4c33e", "data": {"points": null, "type": "CURVED"}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409x07nxa7em9jzx", "label": null, "variableID": "66d5ced37f0b84ecf0511245", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5cf50176e7b205fc4c33c"}, "66d5cf50176e7b205fc4c33d": {"type": "actions", "data": {"name": "", "steps": ["66d5cf50176e7b205fc4c33c"]}, "nodeID": "66d5cf50176e7b205fc4c33d", "coords": [0, 0]}, "66c5a8332825ec011ad625bb": {"type": "block", "data": {"name": "New Block 26", "steps": ["66c5a8332825ec011ad625b9", "66c5a88d2825ec011ad625c2", "66c5a9052825ec011ad625c7"]}, "nodeID": "66c5a8332825ec011ad625bb", "coords": [4814.0774051943645, -445.2916621570328]}, "66c5f49cb9205a485a70e972": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5f472b9205a485a70e969", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66c5f49cb9205a485a70e972"}, "66c5f49cb9205a485a70e973": {"type": "actions", "data": {"name": "", "steps": ["66c5f49cb9205a485a70e972"]}, "nodeID": "66c5f49cb9205a485a70e973", "coords": [0, 0]}, "66d5d413176e7b205fc4c372": {"type": "set-v3", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5ccbe176e7b205fc4c2ee", "id": "66d5d413176e7b205fc4c374", "data": {"points": [{"point": [2889.39, -1362.6], "reversed": true}, {"point": [2865.39, -1362.6]}, {"point": [2865.39, -1795.23]}, {"point": [3163.71, -1795.23], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}, "items": [{"id": "cm0pq54pa409y07nx3v149v2a", "label": null, "variableID": "66d5d4387f0b84ecf05115ff", "type": "script", "value": [{"variableID": "last_utterance"}, " "]}]}, "nodeID": "66d5d413176e7b205fc4c372"}, "66d5d413176e7b205fc4c373": {"type": "actions", "data": {"name": "", "steps": ["66d5d413176e7b205fc4c372"]}, "nodeID": "66d5d413176e7b205fc4c373", "coords": [0, 0]}, "66da130f44e67d133b44d977": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66da130f44e67d133b44d978"}}, "builtIn": {}, "dynamic": []}, "messageID": "66da131c5e1bae3341b56507"}, "nodeID": "66da130f44e67d133b44d977"}, "66da140944e67d133b44d97b": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66da14b644e67d133b44d98e", "id": "66da140944e67d133b44d97c", "data": {}}}, "builtIn": {}, "dynamic": []}, "messageID": "66da14195e1bae3341b56583"}, "nodeID": "66da140944e67d133b44d97b"}, "66da140944e67d133b44d97d": {"type": "block", "data": {"name": "End chat", "steps": ["66da140944e67d133b44d97b"], "color": ""}, "nodeID": "66da140944e67d133b44d97d", "coords": [8106.674380724136, -752.9686581401971]}, "66da147244e67d133b44d983": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "678d9d78c2e29027e4e5ec63", "id": "66da147244e67d133b44d984", "data": {"points": [{"point": [7419.99, -550.96]}, {"point": [7483.51, -550.96]}, {"point": [7483.51, -621.57]}, {"point": [7547.03, -621.57], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}, "messageID": "66da148d5e1bae3341b565db"}, "nodeID": "66da147244e67d133b44d983"}, "66da147244e67d133b44d985": {"type": "block", "data": {"name": "Payment success notification", "steps": ["66da147244e67d133b44d983"], "color": ""}, "nodeID": "66da147244e67d133b44d985", "coords": [7253.804845654446, -681.4937249300905]}, "66da14b644e67d133b44d98d": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da14b644e67d133b44d98d"}, "66da14b644e67d133b44d98e": {"type": "actions", "data": {"name": "", "steps": ["66da14b644e67d133b44d98d"]}, "nodeID": "66da14b644e67d133b44d98e", "coords": [0, 0]}, "66da14e144e67d133b44d993": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66da14e144e67d133b44d994"}}, "builtIn": {}, "dynamic": []}, "messageID": "66da14fe5e1bae3341b56624"}, "nodeID": "66da14e144e67d133b44d993"}, "66da14e144e67d133b44d995": {"type": "block", "data": {"name": "Outlets", "steps": ["66da14e144e67d133b44d993", "66da154a44e67d133b44d99b"], "color": ""}, "nodeID": "66da14e144e67d133b44d995", "coords": [-514.915688797684, -1298.423859478543]}, "66da154a44e67d133b44d99b": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "5jt73a2y", "name": "Enish Knightsbridge", "actions": []}, {"id": "nnto3aq1", "name": "Enish Oxford St.", "actions": []}, {"id": "99tx3ahk", "name": "<PERSON><PERSON>", "actions": []}, {"id": "equ73az3", "name": "Enish Croydon", "actions": [], "intent": null}, {"id": "g0ug3a3d", "name": "<PERSON><PERSON>", "actions": []}, {"id": "03un3ae0", "name": "Enish <PERSON>", "actions": []}, {"id": "ihuu3ana", "name": "Enish <PERSON>", "actions": []}, {"id": "58v13ad8", "name": "Enish Fichley Rd.", "actions": []}, {"id": "b0xo3an4", "name": "Enish Africa Old Kent Rd.", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "66da154a44e67d133b44d99d"}}, "dynamic": [{"type": "", "target": "66da167644e67d133b44d9b2", "id": "66da154a44e67d133b44d99c", "data": {}}, {"id": "66da156744e67d133b44d9a0", "type": "", "target": "66da16ba44e67d133b44d9ba", "data": {}}, {"id": "66da156944e67d133b44d9a3", "type": "", "target": "66da16da44e67d133b44d9c2", "data": {}}, {"id": "66da156d44e67d133b44d9a5", "type": "", "target": "66da170044e67d133b44d9ca", "data": {}}, {"id": "66da157044e67d133b44d9a7", "type": "", "target": "66da172244e67d133b44d9d2", "data": {}}, {"id": "66da157144e67d133b44d9a9", "type": "", "target": "66da173d44e67d133b44d9da", "data": {}}, {"id": "66da157144e67d133b44d9ab", "type": "", "target": "66da176044e67d133b44d9e2", "data": {}}, {"id": "66da157244e67d133b44d9ad", "type": "", "target": "66da177e44e67d133b44d9ea", "data": {}}, {"id": "66da162744e67d133b44d9af", "type": "", "target": "66da179844e67d133b44d9f2", "data": {}}]}}, "nodeID": "66da154a44e67d133b44d99b"}, "66da167644e67d133b44d9b1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr0gx00106357ae0qj3w1d", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da167644e67d133b44d9b3", "data": {"points": [{"point": [-83.74, -1160.08]}, {"point": [42.13, -1160.08]}, {"point": [42.13, -1218.69]}, {"point": [168, -1218.69], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da167644e67d133b44d9b1"}, "66da167644e67d133b44d9b2": {"type": "actions", "data": {"name": "", "steps": ["66da167644e67d133b44d9b1"]}, "nodeID": "66da167644e67d133b44d9b2", "coords": [0, 0]}, "66da16ba44e67d133b44d9b9": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr1wbq011b357a75i11peo", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da16ba44e67d133b44d9bb", "data": {"points": [{"point": [-83.74, -1105.85]}, {"point": [42.13, -1105.85]}, {"point": [42.13, -1218.69]}, {"point": [168, -1218.69], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da16ba44e67d133b44d9b9"}, "66da16ba44e67d133b44d9ba": {"type": "actions", "data": {"name": "", "steps": ["66da16ba44e67d133b44d9b9"]}, "nodeID": "66da16ba44e67d133b44d9ba", "coords": [0, 0]}, "66da16da44e67d133b44d9c1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr2lkl012a357aicapjm2x", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da16da44e67d133b44d9c3", "data": {"points": [{"point": [-83.74, -1051.85]}, {"point": [42.13, -1051.85]}, {"point": [42.13, -1218.69]}, {"point": [168, -1218.69], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da16da44e67d133b44d9c1"}, "66da16da44e67d133b44d9c2": {"type": "actions", "data": {"name": "", "steps": ["66da16da44e67d133b44d9c1"]}, "nodeID": "66da16da44e67d133b44d9c2", "coords": [0, 0]}, "66da170044e67d133b44d9c9": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr3eae013k357anecu5l8w", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da170044e67d133b44d9cb", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da170044e67d133b44d9c9"}, "66da170044e67d133b44d9ca": {"type": "actions", "data": {"name": "", "steps": ["66da170044e67d133b44d9c9"]}, "nodeID": "66da170044e67d133b44d9ca", "coords": [0, 0]}, "66da172244e67d133b44d9d1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr44ly014l357akk858o9w", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da172244e67d133b44d9d3", "data": {"points": [{"point": [-83.74, -943.87]}, {"point": [42.13, -943.87]}, {"point": [42.13, -1218.69]}, {"point": [168, -1218.69], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da172244e67d133b44d9d1"}, "66da172244e67d133b44d9d2": {"type": "actions", "data": {"name": "", "steps": ["66da172244e67d133b44d9d1"]}, "nodeID": "66da172244e67d133b44d9d2", "coords": [0, 0]}, "66da173d44e67d133b44d9d9": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr4ppw015l357a524x53pm", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da173d44e67d133b44d9db", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da173d44e67d133b44d9d9"}, "66da173d44e67d133b44d9da": {"type": "actions", "data": {"name": "", "steps": ["66da173d44e67d133b44d9d9"]}, "nodeID": "66da173d44e67d133b44d9da", "coords": [0, 0]}, "66da176044e67d133b44d9e1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr5h14016p357ae2vanmj2", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da176044e67d133b44d9e3", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da176044e67d133b44d9e1"}, "66da176044e67d133b44d9e2": {"type": "actions", "data": {"name": "", "steps": ["66da176044e67d133b44d9e1"]}, "nodeID": "66da176044e67d133b44d9e2", "coords": [0, 0]}, "66da177e44e67d133b44d9e9": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr640c017s357arfomj3ik", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da177e44e67d133b44d9eb", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da177e44e67d133b44d9e9"}, "66da177e44e67d133b44d9ea": {"type": "actions", "data": {"name": "", "steps": ["66da177e44e67d133b44d9e9"]}, "nodeID": "66da177e44e67d133b44d9ea", "coords": [0, 0]}, "66da179844e67d133b44d9f1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0pr6ngn018w357a1y32aa6r", "type": "script", "label": null, "value": ["", {"variableID": "last_utterance"}, ""], "variableID": "66da16985e1bae3341b5672c"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66d5cde7176e7b205fc4c30d", "id": "66da179844e67d133b44d9f3", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66da179844e67d133b44d9f1"}, "66da179844e67d133b44d9f2": {"type": "actions", "data": {"name": "", "steps": ["66da179844e67d133b44d9f1"]}, "nodeID": "66da179844e67d133b44d9f2", "coords": [0, 0]}, "66db03772621cc581a4c0e3e": {"type": "message", "data": {"name": "", "messageID": "66db0378d8e20c0b54aaec9b", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "66db03772621cc581a4c0e3f"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66db03772621cc581a4c0e3e"}, "66db03772621cc581a4c0e40": {"type": "block", "data": {"name": "Special request", "steps": ["66db03772621cc581a4c0e3e", "66db03ed2621cc581a4c0e46"], "color": ""}, "nodeID": "66db03772621cc581a4c0e40", "coords": [3894.639613896047, -1325.48145859249]}, "66db03ed2621cc581a4c0e46": {"type": "captureV2", "data": {"name": "Capture", "intentScope": "GLOBAL", "capture": {"type": "query", "variable": "last_utterance"}, "noReply": null, "noMatch": null, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "66db04052621cc581a4c0e53", "id": "66db03ed2621cc581a4c0e47", "data": {}}, "else": {"type": "else", "target": null, "id": "66db03ed2621cc581a4c0e48"}}, "dynamic": []}}, "nodeID": "66db03ed2621cc581a4c0e46"}, "66db04052621cc581a4c0e52": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm0qr7xis0135357fmxmjhe77", "type": "script", "label": null, "value": ["{", {"variableID": "last_utterance"}, "}"], "variableID": "66db0437d8e20c0b54aaed3a"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "66c5f992ac196950e2c1e5d3", "id": "66db04052621cc581a4c0e54", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "66db04052621cc581a4c0e52"}, "66db04052621cc581a4c0e53": {"type": "actions", "data": {"name": "", "steps": ["66db04052621cc581a4c0e52"]}, "nodeID": "66db04052621cc581a4c0e53", "coords": [0, 0]}, "66db1b6add6ddc6e4a37c07d": {"type": "block", "data": {"name": "New Block 37", "steps": ["66d2ec14715631612c264633"]}, "nodeID": "66db1b6add6ddc6e4a37c07d", "coords": [861.0406639489194, -3397.546293281882]}, "678bad1d280e612d1aef0259": {"type": "trace", "data": {"name": "", "_v": 1, "paths": [{"label": "", "isDefault": true}], "payload": {"name": "", "body": "", "bodyType": "json", "scope": "local", "isBlocking": false}, "defaultPath": 0, "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": [{"type": "", "target": null, "id": "678bad1d280e612d1aef025a"}]}}, "nodeID": "678bad1d280e612d1aef0259"}, "678bad1d280e612d1aef025b": {"type": "block", "data": {"name": "New Block 38", "steps": ["678bad1d280e612d1aef0259"]}, "nodeID": "678bad1d280e612d1aef025b", "coords": [5770.564565658569, -1084.3201241493223]}, "678d9d78c2e29027e4e5ec63": {"type": "api-v2", "data": {"name": "", "url": [{"text": ["https://[your endpoint subdomain].pythonanywhere.com/api/enish-create-booking/"]}], "headers": [], "httpMethod": "post", "queryParameters": [], "responseMappings": [], "portsV2": {"byKey": {"next": {"type": "next", "target": "66da140944e67d133b44d97b", "id": "678d9d78c2e29027e4e5ec64", "data": {}}, "fail": {"type": "fail", "target": null, "id": "678d9d78c2e29027e4e5ec65"}}, "builtIn": {}, "dynamic": []}, "body": {"type": "form-data", "formData": [{"id": "cm64bwixk00a3357qpo4qxlnl", "key": "first_name", "value": [{"text": ["", {"variableID": "66d5ced37f0b84ecf0511245"}, " "]}]}, {"id": "cm64bx01800ab357qgitxatep", "key": "last_name", "value": [{"text": ["", {"variableID": "66d5cf1d7f0b84ecf051124f"}, " "]}]}, {"id": "cm64bx7yr00ai357q8z2exli6", "key": "mobile_number", "value": [{"text": ["", {"variableID": "66d5d4387f0b84ecf05115ff"}, " "]}]}, {"id": "cm64bxkla00ar357q60kibuut", "key": "restaurant_location", "value": [{"text": ["", {"variableID": "66da16985e1bae3341b5672c"}, " "]}]}, {"id": "cm64bz55v00bb357q4asd5a7x", "key": "appointment_date", "value": [{"text": ["", {"variableID": "678d9e304e984c4ee9b29737"}, " "]}]}, {"id": "cm64bzyfw00bp357qrqkhjxa7", "key": "number_of_guests", "value": [{"text": ["", {"variableID": "66d5adc67f0b84ecf050faf0"}, " "]}]}, {"id": "cm64c0uxv00c4357qje7dsl8c", "key": "email", "value": [{"text": ["", {"variableID": "66d5cfa37f0b84ecf05112b1"}, " "]}]}]}}, "nodeID": "678d9d78c2e29027e4e5ec63"}, "678d9d78c2e29027e4e5ec66": {"type": "block", "data": {"name": "Send email notification", "steps": ["678d9d78c2e29027e4e5ec63"], "color": ""}, "nodeID": "678d9d78c2e29027e4e5ec66", "coords": [7713.209848517248, -700.3861230618711]}, "678d9f59c2e29027e4e5ec6e": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c6dlk00ik357qzkzttdys", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec617f0b84ecf04fc97f"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678d9f59c2e29027e4e5ec6f"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678d9f59c2e29027e4e5ec6e"}, "678d9facc2e29027e4e5ec71": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c85i400lw357qu4whco8f", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec6d7f0b84ecf04fc980"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678d9facc2e29027e4e5ec72"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678d9facc2e29027e4e5ec71"}, "678d9fd3c2e29027e4e5ec74": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c8yq700nz357qx0d7oplj", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec777f0b84ecf04fc981"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678d9fd3c2e29027e4e5ec75"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678d9fd3c2e29027e4e5ec74"}, "678d9ff1c2e29027e4e5ec77": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c9nqn00pp357qkysmnv34", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec7f7f0b84ecf04fc982"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678d9ff1c2e29027e4e5ec78"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678d9ff1c2e29027e4e5ec77"}, "678da01dc2e29027e4e5ec7a": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cakew00rp357qclwqwa5j", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec8b7f0b84ecf04fc985"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da01dc2e29027e4e5ec7b"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da01dc2e29027e4e5ec7a"}, "678da043c2e29027e4e5ec7d": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cbdag00ta357qv0oseryd", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec997f0b84ecf04fc991"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da043c2e29027e4e5ec7e"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da043c2e29027e4e5ec7d"}, "678da061c2e29027e4e5ec80": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cc0n000us357qxgiytktu", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2eca47f0b84ecf04fc9a6"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da061c2e29027e4e5ec81"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da061c2e29027e4e5ec80"}, "678da095c2e29027e4e5ec83": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cd4ti00wd357qk6jd7c7j", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ecbb7f0b84ecf04fc9a9"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da095c2e29027e4e5ec84"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da095c2e29027e4e5ec83"}, "678da0b5c2e29027e4e5ec86": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cdt3w00xq357qdynjjeft", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed067f0b84ecf04fc9ba"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da0b5c2e29027e4e5ec87"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da0b5c2e29027e4e5ec86"}, "678da0d4c2e29027e4e5ec89": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cehn100yy357qzz0sd83f", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed117f0b84ecf04fc9bd"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da0d4c2e29027e4e5ec8a"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da0d4c2e29027e4e5ec89"}, "678da117c2e29027e4e5ec8d": {"type": "block", "data": {"name": "New Block 29", "color": "", "steps": ["678da117c2e29027e4e5eca3"]}, "nodeID": "678da117c2e29027e4e5ec8d", "coords": [1068.935020080718, -2374.4601396335074]}, "678da117c2e29027e4e5ec8f": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecb1", "678da117c2e29027e4e5ecce"]}, "nodeID": "678da117c2e29027e4e5ec8f", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec91": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecb4", "678da117c2e29027e4e5eccf"]}, "nodeID": "678da117c2e29027e4e5ec91", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec93": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecb7", "678da117c2e29027e4e5ecd0"]}, "nodeID": "678da117c2e29027e4e5ec93", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec95": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecba", "678da117c2e29027e4e5ecd1"]}, "nodeID": "678da117c2e29027e4e5ec95", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec97": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecbd", "678da117c2e29027e4e5ecd2"]}, "nodeID": "678da117c2e29027e4e5ec97", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec99": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecc0", "678da117c2e29027e4e5ecd3"]}, "nodeID": "678da117c2e29027e4e5ec99", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec9b": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecc3", "678da117c2e29027e4e5ecd4"]}, "nodeID": "678da117c2e29027e4e5ec9b", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec9d": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecc6", "678da117c2e29027e4e5ecd5"]}, "nodeID": "678da117c2e29027e4e5ec9d", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5ec9f": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5ecc9", "678da117c2e29027e4e5ecd6"]}, "nodeID": "678da117c2e29027e4e5ec9f", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5eca1": {"type": "actions", "data": {"name": "", "steps": ["678da117c2e29027e4e5eccc", "678da117c2e29027e4e5ecd7"]}, "nodeID": "678da117c2e29027e4e5eca1", "coords": [39.999999999999886, 40]}, "678da117c2e29027e4e5eca3": {"type": "buttons", "data": {"name": "Buttons", "buttons": [{"id": "btet3aeq", "name": "{{[firstDate].66d2ec617f0b84ecf04fc97f}}", "actions": []}, {"id": "xsf63ar8", "name": "{{[secondDate].66d2ec6d7f0b84ecf04fc980}}", "actions": []}, {"id": "2ifi3abb", "name": "{{[thirdDate].66d2ec777f0b84ecf04fc981}}", "actions": []}, {"id": "pefs3axl", "name": "{{[fourthDate].66d2ec7f7f0b84ecf04fc982}}", "actions": []}, {"id": "j5g03agl", "name": "{{[fifthDate].66d2ec8b7f0b84ecf04fc985}}", "actions": [], "intent": null}, {"id": "1yg73ab4", "name": "{{[sixthDate].66d2ec997f0b84ecf04fc991}}", "actions": []}, {"id": "gcge3aag", "name": "{{[seventhDate].66d2eca47f0b84ecf04fc9a6}}", "actions": []}, {"id": "pogn3awy", "name": "{{[eighthDate].66d2ecbb7f0b84ecf04fc9a9}}", "actions": []}, {"id": "77h53ajm", "name": "{{[ninthDate].66d2ed067f0b84ecf04fc9ba}}", "actions": []}, {"id": "6jhd3at8", "name": "{{[tenthDate].66d2ed117f0b84ecf04fc9bd}}", "actions": []}, {"id": "upfo3eob", "name": "Show more", "actions": []}], "intentScope": "GLOBAL", "noMatch": {"types": [], "pathName": "No match", "randomize": false, "reprompts": []}, "portsV2": {"byKey": {}, "builtIn": {"else": {"type": "else", "target": null, "id": "678da117c2e29027e4e5eca4"}}, "dynamic": [{"type": "", "target": "678da117c2e29027e4e5ec8f", "id": "678da117c2e29027e4e5eca5", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec91", "id": "678da117c2e29027e4e5eca6", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec93", "id": "678da117c2e29027e4e5eca7", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec95", "id": "678da117c2e29027e4e5eca8", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec97", "id": "678da117c2e29027e4e5eca9", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec99", "id": "678da117c2e29027e4e5ecaa", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec9b", "id": "678da117c2e29027e4e5ecab", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec9d", "id": "678da117c2e29027e4e5ecac", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5ec9f", "id": "678da117c2e29027e4e5ecad", "data": {}}, {"type": "", "target": "678da117c2e29027e4e5eca1", "id": "678da117c2e29027e4e5ecae", "data": {}}, {"type": "", "target": null, "id": "678da117c2e29027e4e5ecaf", "data": {"points": [{"point": [902.75, -1755.23], "reversed": true}, {"point": [507.84, -1755.23]}, {"point": [507.84, -1596.21]}, {"point": [112.93, -1596.21], "reversed": true, "allowedToTop": false}]}}]}}, "nodeID": "678da117c2e29027e4e5eca3"}, "678da117c2e29027e4e5ecb1": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c6dlk00ik357qzkzttdys", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec617f0b84ecf04fc97f"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecb2"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecb1"}, "678da117c2e29027e4e5ecb4": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c85i400lw357qu4whco8f", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec6d7f0b84ecf04fc980"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecb5"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecb4"}, "678da117c2e29027e4e5ecb7": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c8yq700nz357qx0d7oplj", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec777f0b84ecf04fc981"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecb8"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecb7"}, "678da117c2e29027e4e5ecba": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64c9nqn00pp357qkysmnv34", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec7f7f0b84ecf04fc982"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecbb"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecba"}, "678da117c2e29027e4e5ecbd": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cakew00rp357qclwqwa5j", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec8b7f0b84ecf04fc985"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecbe"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecbd"}, "678da117c2e29027e4e5ecc0": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cbdag00ta357qv0oseryd", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec997f0b84ecf04fc991"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecc1"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecc0"}, "678da117c2e29027e4e5ecc3": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cc0n000us357qxgiytktu", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2eca47f0b84ecf04fc9a6"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecc4"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecc3"}, "678da117c2e29027e4e5ecc6": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cd4ti00wd357qk6jd7c7j", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ecbb7f0b84ecf04fc9a9"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecc7"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecc6"}, "678da117c2e29027e4e5ecc9": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cdt3w00xq357qdynjjeft", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed067f0b84ecf04fc9ba"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5ecca"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecc9"}, "678da117c2e29027e4e5eccc": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64cehn100yy357qzz0sd83f", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed117f0b84ecf04fc9bd"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da117c2e29027e4e5eccd"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5eccc"}, "678da117c2e29027e4e5ecce": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecce"}, "678da117c2e29027e4e5eccf": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5eccf"}, "678da117c2e29027e4e5ecd0": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd0"}, "678da117c2e29027e4e5ecd1": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd1"}, "678da117c2e29027e4e5ecd2": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd2"}, "678da117c2e29027e4e5ecd3": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd3"}, "678da117c2e29027e4e5ecd4": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd4"}, "678da117c2e29027e4e5ecd5": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd5"}, "678da117c2e29027e4e5ecd6": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd6"}, "678da117c2e29027e4e5ecd7": {"type": "goToNode", "data": {"name": "", "nodeID": "66c5a1d22825ec011ad62463", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da117c2e29027e4e5ecd7"}, "678da963c2e29027e4e5ed1c": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678da963c2e29027e4e5ed1d"}}, "builtIn": {}, "dynamic": []}, "messageID": "678da96a4e984c4ee9b29b4c"}, "nodeID": "678da963c2e29027e4e5ed1c"}, "678da963c2e29027e4e5ed1e": {"type": "block", "data": {"name": "New Block 41", "steps": ["678da963c2e29027e4e5ed1c", "678da9b4c2e29027e4e5ed25"]}, "nodeID": "678da963c2e29027e4e5ed1e", "coords": [-969.6943813450835, -801.7435275499747]}, "678da9b4c2e29027e4e5ed25": {"type": "capture-v3", "data": {"name": "Capture", "capture": {"type": "user-reply", "variableID": "last_utterance"}, "listenForOtherTriggers": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "678da9c3c2e29027e4e5ed2c", "id": "678da9b4c2e29027e4e5ed26", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da9b4c2e29027e4e5ed25", "coords": [0, 0]}, "678da9c3c2e29027e4e5ed2b": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64drig701tw357qjc9855bc", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "last_utterance"}, " "]}], "variableID": "678da9d74e984c4ee9b29b66"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "678daa86c2e29027e4e5ed36", "id": "678da9c3c2e29027e4e5ed2d", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678da9c3c2e29027e4e5ed2b"}, "678da9c3c2e29027e4e5ed2c": {"type": "actions", "data": {"name": "", "steps": ["678da9c3c2e29027e4e5ed2b"]}, "nodeID": "678da9c3c2e29027e4e5ed2c", "coords": [0, 0]}, "678daa86c2e29027e4e5ed33": {"type": "code", "data": {"name": "", "code": "function trimWhitespace(str) {\r\n  return str.trim();\r\n}\r\n\r\n\r\nbooking_code = trimWhitespace(booking_code.toString());", "paths": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678daaf1c2e29027e4e5ed41", "id": "678daa86c2e29027e4e5ed34", "data": {"points": [{"point": [-721.09, -212.89]}, {"point": [-657.08, -212.89]}, {"point": [-657.08, -341.45]}, {"point": [-593.06, -341.45], "allowedToTop": true}]}}, "fail": {"type": "fail", "target": null, "id": "678daa86c2e29027e4e5ed35"}}, "dynamic": []}}, "nodeID": "678daa86c2e29027e4e5ed33"}, "678daa86c2e29027e4e5ed36": {"type": "block", "data": {"name": "Remove withspace from code", "steps": ["678daa86c2e29027e4e5ed33"], "color": ""}, "nodeID": "678daa86c2e29027e4e5ed36", "coords": [-887.2738589764547, -346.43744980689985]}, "678daaf1c2e29027e4e5ed3e": {"type": "api-v2", "data": {"name": "", "url": [{"text": ["https://[your endpoint subdomain].pythonanywhere.com/api/canel-enish-booking/", {"variableID": "678da9d74e984c4ee9b29b66"}, "/"]}], "headers": [], "httpMethod": "get", "queryParameters": [], "responseMappings": [{"id": "cm64e0abh01yy357qw3x3g3lh", "path": "first_name", "variableID": "66d5ced37f0b84ecf0511245"}, {"id": "cm64e0g6701z1357qzp48px1k", "path": "last_name", "variableID": "66d5cf1d7f0b84ecf051124f"}, {"id": "cm64e0ltg01z4357qldoy38kd", "path": "email", "variableID": "66d5cfa37f0b84ecf05112b1"}, {"id": "cm64e0qfx01z7357q2wb7w6dl", "path": "mobile_number", "variableID": "66d5d4387f0b84ecf05115ff"}, {"id": "cm64e0ylh01za357qmtsudhrg", "path": "restaurant_location", "variableID": "678d9e094e984c4ee9b2972a"}, {"id": "cm64e17zx01ze357qdvxwdlym", "path": "number_of_guests", "variableID": "66d5adc67f0b84ecf050faf0"}, {"id": "cm64e1g8q01zi357q377rfwcs", "path": "appointment_date", "variableID": "678d9e304e984c4ee9b29737"}], "portsV2": {"byKey": {"next": {"type": "next", "target": "678dabd1c2e29027e4e5ed4e", "id": "678daaf1c2e29027e4e5ed3f", "data": {}}, "fail": {"id": "678dabb0c2e29027e4e5ed49", "target": "678dacccc2e29027e4e5ed5e", "type": "fail", "data": {"points": [{"point": [-261.88, -127.11]}, {"point": [-228.22, -127.11]}, {"point": [-228.22, -11.37], "toTop": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}, "fallback": {"path": true, "pathLabel": "Not Found"}}, "nodeID": "678daaf1c2e29027e4e5ed3e"}, "678daaf1c2e29027e4e5ed41": {"type": "block", "data": {"name": "get booking details", "steps": ["678daaf1c2e29027e4e5ed3e"], "color": ""}, "nodeID": "678daaf1c2e29027e4e5ed41", "coords": [-428.0592517323354, -368.4539735617693]}, "678dabd1c2e29027e4e5ed4c": {"type": "message", "data": {"name": "", "messageID": "678dabd24e984c4ee9b29baa", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678dabd1c2e29027e4e5ed4d"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dabd1c2e29027e4e5ed4c"}, "678dabd1c2e29027e4e5ed4e": {"type": "block", "data": {"name": "Summarize booking", "steps": ["678dabd1c2e29027e4e5ed4c", "678dacaac2e29027e4e5ed55"], "color": ""}, "nodeID": "678dabd1c2e29027e4e5ed4e", "coords": [-880.7578953900356, -10.747832179783458]}, "678dacaac2e29027e4e5ed55": {"type": "buttons-v2", "data": {"name": "Buttons", "items": [{"id": "cm64e7ejj023e357qtbi93la5", "label": [{"text": ["Yes"]}]}, {"id": "cm64e7hp8023o357q66mt3wjx", "label": [{"text": ["No"]}]}], "listenForOtherTriggers": true, "portsV2": {"byKey": {"cm64e7ejj023e357qtbi93la5": {"id": "678dacacc2e29027e4e5ed58", "target": "678daefdc2e29027e4e5ed95", "type": "", "data": {"points": [{"point": [-714.58, 387.2]}, {"point": [-688.78, 387.2]}, {"point": [-688.78, 426.38]}, {"point": [-662.99, 426.38], "allowedToTop": true}]}}, "cm64e7hp8023o357q66mt3wjx": {"id": "678dacb1c2e29027e4e5ed5a", "target": "678dadd4c2e29027e4e5ed7c", "type": "", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dacaac2e29027e4e5ed55"}, "678dacccc2e29027e4e5ed5c": {"type": "message", "data": {"name": "", "messageID": "678daccd4e984c4ee9b29bca", "portsV2": {"byKey": {"next": {"type": "next", "target": "678dad18c2e29027e4e5ed66", "id": "678dacccc2e29027e4e5ed5d", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dacccc2e29027e4e5ed5c"}, "678dacccc2e29027e4e5ed5e": {"type": "block", "data": {"name": "No booking code match", "steps": ["678dacccc2e29027e4e5ed5c"], "color": ""}, "nodeID": "678dacccc2e29027e4e5ed5e", "coords": [-228.22042211389794, -11.37376939531919]}, "678dad18c2e29027e4e5ed65": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dad18c2e29027e4e5ed65"}, "678dad18c2e29027e4e5ed66": {"type": "actions", "data": {"name": "", "steps": ["678dad18c2e29027e4e5ed65"]}, "nodeID": "678dad18c2e29027e4e5ed66", "coords": [0, 0]}, "678dad5bc2e29027e4e5ed6c": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678dad5bc2e29027e4e5ed6d"}}, "builtIn": {}, "dynamic": []}, "messageID": "678dad624e984c4ee9b29be4"}, "nodeID": "678dad5bc2e29027e4e5ed6c"}, "678dad5bc2e29027e4e5ed6e": {"type": "block", "data": {"name": "Get OTP", "steps": ["678dad5bc2e29027e4e5ed6c", "678dadc6c2e29027e4e5ed75"], "color": ""}, "nodeID": "678dad5bc2e29027e4e5ed6e", "coords": [141.20457244751896, 329.4670979481747]}, "678dadc6c2e29027e4e5ed75": {"type": "capture-v3", "data": {"name": "Capture", "capture": {"type": "user-reply", "variableID": "last_utterance"}, "listenForOtherTriggers": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "678daea9c2e29027e4e5ed8b", "id": "678dadc6c2e29027e4e5ed76", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dadc6c2e29027e4e5ed75"}, "678dadd4c2e29027e4e5ed7a": {"type": "message", "data": {"name": "", "messageID": "678dadd54e984c4ee9b29bf9", "portsV2": {"byKey": {"next": {"type": "next", "target": "678dae97c2e29027e4e5ed84", "id": "678dadd4c2e29027e4e5ed7b", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dadd4c2e29027e4e5ed7a"}, "678dadd4c2e29027e4e5ed7c": {"type": "block", "data": {"name": "New Block 47", "steps": ["678dadd4c2e29027e4e5ed7a"]}, "nodeID": "678dadd4c2e29027e4e5ed7c", "coords": [-955.1896580853482, 684.5021678202165]}, "678dae97c2e29027e4e5ed83": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dae97c2e29027e4e5ed83"}, "678dae97c2e29027e4e5ed84": {"type": "actions", "data": {"name": "", "steps": ["678dae97c2e29027e4e5ed83"]}, "nodeID": "678dae97c2e29027e4e5ed84", "coords": [0, 0]}, "678daea9c2e29027e4e5ed8a": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64eided02fn357qv5xt5ke1", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "last_utterance"}, " "]}], "variableID": "678daec64e984c4ee9b29c1e"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "678dafa5c2e29027e4e5eda0", "id": "678daea9c2e29027e4e5ed8c", "data": {"points": null, "type": "CURVED"}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678daea9c2e29027e4e5ed8a"}, "678daea9c2e29027e4e5ed8b": {"type": "actions", "data": {"name": "", "steps": ["678daea9c2e29027e4e5ed8a"]}, "nodeID": "678daea9c2e29027e4e5ed8b", "coords": [0, 0]}, "678daefdc2e29027e4e5ed92": {"type": "api-v2", "data": {"name": "", "url": [{"text": ["https://[your endpoint subdomain].pythonanywhere.com/api/custom-otp-generator"]}], "headers": [], "httpMethod": "post", "queryParameters": [], "responseMappings": [{"id": "cm64elqxk02ih357qmxz3e02e", "path": "otp", "variableID": "678daf494e984c4ee9b29c47"}, {"id": "cm64em73j02io357qfmmjnxkm", "path": "created_at", "variableID": "678daf5e4e984c4ee9b29c49"}], "portsV2": {"byKey": {"next": {"type": "next", "target": "678dad5bc2e29027e4e5ed6e", "id": "678daefdc2e29027e4e5ed93", "data": {"points": [{"point": [-331.81, 496.46]}, {"point": [-177.8, 496.46]}, {"point": [-177.8, 356.47]}, {"point": [-23.8, 356.47], "allowedToTop": true}]}}, "fail": {"id": "678db27ec2e29027e4e5edfc", "target": null, "type": "fail"}}, "builtIn": {}, "dynamic": []}, "body": {"type": "form-data", "formData": [{"id": "cm64ekjno02hq357qt4rhu7az", "key": "email", "value": [{"text": ["", {"variableID": "66d5cfa37f0b84ecf05112b1"}, " "]}]}]}, "fallback": {"path": true, "pathLabel": "Failure"}}, "nodeID": "678daefdc2e29027e4e5ed92"}, "678daefdc2e29027e4e5ed95": {"type": "block", "data": {"name": "Generate OTP", "steps": ["678daefdc2e29027e4e5ed92"], "color": ""}, "nodeID": "678daefdc2e29027e4e5ed95", "coords": [-497.98952789515477, 399.38319845788016]}, "678dafa5c2e29027e4e5ed9d": {"type": "code", "data": {"name": "", "code": "function trimWhitespace(str) {\r\n  return str.trim();\r\n}\r\n\r\n\r\nprovided_otp = trimWhitespace(provided_otp.toString());", "paths": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db085c2e29027e4e5edc9", "id": "678dafa5c2e29027e4e5ed9e", "data": {}}, "fail": {"type": "fail", "target": null, "id": "678dafa5c2e29027e4e5ed9f"}}, "dynamic": []}}, "nodeID": "678dafa5c2e29027e4e5ed9d"}, "678dafa5c2e29027e4e5eda0": {"type": "block", "data": {"name": "Remove whitespace from OTP", "steps": ["678dafa5c2e29027e4e5ed9d"], "color": ""}, "nodeID": "678dafa5c2e29027e4e5eda0", "coords": [-325.0299455713418, 892.4470823959147]}, "678db085c2e29027e4e5edc9": {"type": "code", "data": {"name": "", "code": "// convert ISO format to epoch time\notp_timestamp_epoch = Math.floor(new Date(otp_timestamp).getTime() / 1000);\n\n\n// Get current epoch time\ncurrent_timestamp = Math.floor(Date.now() / 1000);\n\n\n// Calculate elapsed timestamp in seconds \n elapsed_timestamp = current_timestamp - otp_timestamp_epoch", "paths": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db0d8c2e29027e4e5edd7", "id": "678db085c2e29027e4e5edca", "data": {}}, "fail": {"type": "fail", "target": null, "id": "678db085c2e29027e4e5edcb"}}, "dynamic": []}}, "nodeID": "678db085c2e29027e4e5edc9"}, "678db085c2e29027e4e5edcc": {"type": "block", "data": {"name": "Convert OTP timestamp", "steps": ["678db085c2e29027e4e5edc9"], "color": ""}, "nodeID": "678db085c2e29027e4e5edcc", "coords": [152.54502054664962, 845.9803289357856]}, "678db0d8c2e29027e4e5edd4": {"type": "condition-v3", "data": {"name": "", "noMatch": {"path": true, "repromptID": null}, "condition": {"type": "logic", "items": [{"id": "cm64ev0em02qm357qx82sjmve", "value": {"type": "script", "code": ["", {"variableID": "678db06d4e984c4ee9b29cb9"}, " <= 500 && ", {"variableID": "678daec64e984c4ee9b29c1e"}, " == ", {"variableID": "678daf494e984c4ee9b29c47"}, ""]}}, {"id": "cm64ewwos02ri357qhcggp4dt", "value": {"type": "script", "code": ["", {"variableID": "678daec64e984c4ee9b29c1e"}, " != ", {"variableID": "678daf494e984c4ee9b29c47"}, ""]}}, {"id": "cm66o0g9p088s357jxlzz8if2", "value": {"type": "script", "code": ["", {"variableID": "678db06d4e984c4ee9b29cb9"}, " > 500"]}}]}, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678db0d8c2e29027e4e5edd5"}, "else": {"type": "else", "target": "678db1a9c2e29027e4e5ede4", "id": "678db0d8c2e29027e4e5edd6", "data": {}}, "cm64ev0em02qm357qx82sjmve": {"id": "678db0fac2e29027e4e5eddd", "target": "678db54cc2e29027e4e5ee11", "type": "", "data": {"points": [{"point": [155.17, 1356.77]}, {"point": [240.25, 1356.77]}, {"point": [240.25, 1320.62]}, {"point": [325.32, 1320.62], "allowedToTop": false}]}}, "cm64ewwos02ri357qhcggp4dt": {"id": "678db152c2e29027e4e5ede0", "target": "678db22ac2e29027e4e5edeb", "type": "", "data": {"points": [{"point": [155.17, 1420.67]}, {"point": [200.23, 1420.67]}, {"point": [200.23, 1812.15]}, {"point": [245.28, 1812.15], "allowedToTop": false}]}}, "cm66o0g9p088s357jxlzz8if2": {"id": "678fc569d514b7d70b3c2bd0", "target": "678fc57ed514b7d70b3c2bd4", "type": "", "data": {"points": [{"point": [155.17, 1474.67]}, {"point": [498.02, 1474.67]}, {"point": [498.02, 1696.07]}, {"point": [840.87, 1696.07], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db0d8c2e29027e4e5edd4"}, "678db0d8c2e29027e4e5edd7": {"type": "block", "data": {"name": "New Block 51", "steps": ["678db0d8c2e29027e4e5edd4"]}, "nodeID": "678db0d8c2e29027e4e5edd7", "coords": [-11.00772440162432, 1268.0533395319565]}, "678db1a9c2e29027e4e5ede2": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "678fc5e8d514b7d70b3c2be9", "id": "678db1a9c2e29027e4e5ede3", "data": {}}}, "builtIn": {}, "dynamic": []}, "messageID": "678db1b34e984c4ee9b29d7e"}, "nodeID": "678db1a9c2e29027e4e5ede2"}, "678db1a9c2e29027e4e5ede4": {"type": "block", "data": {"name": "New Block 52", "steps": ["678db1a9c2e29027e4e5ede2"]}, "nodeID": "678db1a9c2e29027e4e5ede4", "coords": [-124.89378832809517, 1709.350800132883]}, "678db22ac2e29027e4e5edeb": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678db22ac2e29027e4e5edec"}}, "builtIn": {}, "dynamic": []}, "messageID": "678db2304e984c4ee9b29e0d"}, "nodeID": "678db22ac2e29027e4e5edeb"}, "678db22ac2e29027e4e5eded": {"type": "block", "data": {"name": "New Block 53", "steps": ["678db22ac2e29027e4e5edeb", "678db255c2e29027e4e5edf3"]}, "nodeID": "678db22ac2e29027e4e5eded", "coords": [411.46377192618513, 1733.3274866118013]}, "678db255c2e29027e4e5edf3": {"type": "buttons-v2", "data": {"name": "Buttons", "items": [{"id": "cm64f2ijh02wo357q1fz1psqg", "label": [{"text": ["Yes"]}]}, {"id": "cm64f2lbr02wy357q7kul5gg2", "label": [{"text": ["No"]}]}], "listenForOtherTriggers": true, "portsV2": {"byKey": {"cm64f2ijh02wo357q1fz1psqg": {"id": "678db258c2e29027e4e5edf6", "target": "678db297c2e29027e4e5ee00", "type": "", "data": {}}, "cm64f2lbr02wy357q7kul5gg2": {"id": "678db25cc2e29027e4e5edf8", "target": "678db1a9c2e29027e4e5ede4", "type": "", "data": {"points": [{"point": [245.28, 1946.43], "reversed": true}, {"point": [142.69, 1946.43]}, {"point": [142.69, 1736.35]}, {"point": [40.11, 1736.35], "reversed": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db255c2e29027e4e5edf3"}, "678db297c2e29027e4e5edff": {"type": "goToNode", "data": {"name": "", "nodeID": "678daefdc2e29027e4e5ed95", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db297c2e29027e4e5edff"}, "678db297c2e29027e4e5ee00": {"type": "actions", "data": {"name": "", "steps": ["678db297c2e29027e4e5edff"]}, "nodeID": "678db297c2e29027e4e5ee00", "coords": [0, 0]}, "678db2c0c2e29027e4e5ee06": {"type": "message", "data": {"name": "", "messageID": "678db2c24e984c4ee9b29ec0", "portsV2": {"byKey": {"next": {"type": "next", "target": "678db742c2e29027e4e5ee22", "id": "678db2c0c2e29027e4e5ee07", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db2c0c2e29027e4e5ee06"}, "678db2c0c2e29027e4e5ee08": {"type": "block", "data": {"name": "New Block 54", "steps": ["678db2c0c2e29027e4e5ee06"]}, "nodeID": "678db2c0c2e29027e4e5ee08", "coords": [943.3127887782205, 1132.9812465868865]}, "678db54cc2e29027e4e5ee11": {"type": "api-v2", "data": {"name": "", "url": [{"text": ["https://[your endpoint subdomain].pythonanywhere.com/api/canel-enish-booking/", {"variableID": "678da9d74e984c4ee9b29b66"}, "/"]}], "headers": [], "fallback": {"path": true, "pathLabel": "Failure"}, "httpMethod": "patch", "queryParameters": [], "responseMappings": [], "portsV2": {"byKey": {"next": {"type": "next", "target": "678db2c0c2e29027e4e5ee06", "id": "678db54cc2e29027e4e5ee12", "data": {}}, "fail": {"id": "678db556c2e29027e4e5ee1d", "target": null, "type": "fail"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db54cc2e29027e4e5ee11"}, "678db54cc2e29027e4e5ee14": {"type": "block", "data": {"name": "New Block 55", "steps": ["678db54cc2e29027e4e5ee11"]}, "nodeID": "678db54cc2e29027e4e5ee14", "coords": [491.5032795658839, 1241.7982087293515]}, "678db742c2e29027e4e5ee21": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db742c2e29027e4e5ee21"}, "678db742c2e29027e4e5ee22": {"type": "actions", "data": {"name": "", "steps": ["678db742c2e29027e4e5ee21"]}, "nodeID": "678db742c2e29027e4e5ee22", "coords": [0, 0]}, "678e1fff32bdf36a07744628": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vs9dv00tf357rowm737a6", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec617f0b84ecf04fc97f"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e1fff32bdf36a07744629"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e1fff32bdf36a07744628"}, "678e202d32bdf36a0774462b": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vt8dj00vz357rgyfuxkfh", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec6d7f0b84ecf04fc980"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e202d32bdf36a0774462c"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e202d32bdf36a0774462b"}, "678e204632bdf36a0774462e": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vtreb00xu357ry5mdzpy0", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec777f0b84ecf04fc981"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e204632bdf36a0774462f"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e204632bdf36a0774462e"}, "678e206232bdf36a07744631": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vudu500zm357rsfjjk04e", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec7f7f0b84ecf04fc982"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e206232bdf36a07744632"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e206232bdf36a07744631"}, "678e208032bdf36a07744634": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vv0t20119357rpvldr9po", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec8b7f0b84ecf04fc985"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e208032bdf36a07744635"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e208032bdf36a07744634"}, "678e209f32bdf36a07744637": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vvpf4012z357rptojxtdl", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ec997f0b84ecf04fc991"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e209f32bdf36a07744638"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e209f32bdf36a07744637"}, "678e20d732bdf36a0774463a": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vwuys014l357r4vb9jdfn", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2eca47f0b84ecf04fc9a6"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e20d732bdf36a0774463b"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e20d732bdf36a0774463a"}, "678e20f032bdf36a0774463d": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vxf5w015z357rcrf5b5jp", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ecbb7f0b84ecf04fc9a9"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e20f032bdf36a0774463e"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e20f032bdf36a0774463d"}, "678e211232bdf36a07744640": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vy4tv017b357riqmcpdnh", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed067f0b84ecf04fc9ba"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e211232bdf36a07744641"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e211232bdf36a07744640"}, "678e212d32bdf36a07744643": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm64vypg0018k357rhbi7emzi", "type": "value", "label": null, "value": [{"text": ["", {"variableID": "66d2ed117f0b84ecf04fc9bd"}, " "]}], "variableID": "678d9e304e984c4ee9b29737"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678e212d32bdf36a07744644"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e212d32bdf36a07744643"}, "678fc57ed514b7d70b3c2bd2": {"type": "message", "data": {"name": "", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678fc57ed514b7d70b3c2bd3"}}, "builtIn": {}, "dynamic": []}, "messageID": "678fc5923a5bd540470e71eb"}, "nodeID": "678fc57ed514b7d70b3c2bd2"}, "678fc57ed514b7d70b3c2bd4": {"type": "block", "data": {"name": "Expired OTP", "steps": ["678fc57ed514b7d70b3c2bd2", "678fc5c3d514b7d70b3c2bdb"], "color": ""}, "nodeID": "678fc57ed514b7d70b3c2bd4", "coords": [1005.8653929833856, 1669.065940482685]}, "678fc5c3d514b7d70b3c2bdb": {"type": "buttons-v2", "data": {"name": "Buttons", "items": [{"id": "cm66o2gq808bz357j73bfbzr1", "label": [{"text": ["Yes"]}]}, {"id": "cm66o2jd608c8357js5oppvps", "label": [{"text": ["No"]}]}], "listenForOtherTriggers": true, "portsV2": {"byKey": {"cm66o2gq808bz357j73bfbzr1": {"id": "678fc5c6d514b7d70b3c2bde", "target": "678fc5d7d514b7d70b3c2be3", "type": "", "data": {}}, "cm66o2jd608c8357js5oppvps": {"id": "678fc5cad514b7d70b3c2be0", "target": "678db1a9c2e29027e4e5ede2", "type": "", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678fc5c3d514b7d70b3c2bdb"}, "678fc5d7d514b7d70b3c2be2": {"type": "goToNode", "data": {"name": "", "nodeID": "678daefdc2e29027e4e5ed95", "diagramID": "64386e21bb106b044ea34b78", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678fc5d7d514b7d70b3c2be2"}, "678fc5d7d514b7d70b3c2be3": {"type": "actions", "data": {"name": "", "steps": ["678fc5d7d514b7d70b3c2be2"]}, "nodeID": "678fc5d7d514b7d70b3c2be3", "coords": [0, 0]}, "678fc5e8d514b7d70b3c2be8": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678fc5e8d514b7d70b3c2be8"}, "678fc5e8d514b7d70b3c2be9": {"type": "actions", "data": {"name": "", "steps": ["678fc5e8d514b7d70b3c2be8"]}, "nodeID": "678fc5e8d514b7d70b3c2be9", "coords": [0, 0]}}, "offsetX": 1641.7555050760668, "offsetY": 1022.5948220399798, "modified": 1681419809, "creatorID": 1464246, "variables": [], "menuItems": [{"type": "NODE", "sourceID": "start00000000000000000000"}], "menuNodeIDs": [], "intentStepIDs": [], "_id": "678bb820c4618d115e6b0b9c", "diagramID": "64386e21bb106b044ea34b78", "versionID": "678bb820c4618d115e6b0b98"}}, "flows": [], "entities": [], "entityVariants": [], "intents": [{"id": "6579c6bf984565862f64b783", "name": "Yes", "createdByID": 1464246, "folderID": null, "description": "Trigger this intent when the user responds affirmatively or agrees to a question or statement using words like \"yes\", \"yea\", \"yup\", \"yep\", \"ya\", \"ok\", \"okay\", \"affirmative\", \"certainly\", or \"sure\".", "automaticReprompt": false, "entityOrder": [], "automaticRepromptSettings": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "6579c778dd3cca0fe0e0b152", "name": "No", "createdByID": 1464246, "folderID": null, "description": "Trigger this intent when the user responds negatively or declines something, using words like \"no\", \"nope\", \"nay\", \"nah\", \"not now\", \"no way\", \"no thanks\", \"not\", \"nevermind\", or \"negative\".", "automaticReprompt": false, "entityOrder": [], "automaticRepromptSettings": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "None", "name": "None", "createdByID": 1464246, "folderID": null, "description": null, "automaticReprompt": false, "entityOrder": [], "automaticRepromptSettings": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}], "utterances": [{"id": "65d72a6aa845250f3bd1b9af", "text": [{"text": ["sure"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b0", "text": [{"text": ["certainly"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b1", "text": [{"text": ["affirmative"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b2", "text": [{"text": ["okay"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b3", "text": [{"text": ["ok"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b4", "text": [{"text": ["ya"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b5", "text": [{"text": ["yep"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b6", "text": [{"text": ["yup"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b7", "text": [{"text": ["yea"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b8", "text": [{"text": ["yes"]}], "intentID": "6579c6bf984565862f64b783", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9b9", "text": [{"text": ["negative"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9ba", "text": [{"text": ["nevermind"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9bb", "text": [{"text": ["not"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9bc", "text": [{"text": ["no thanks"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9bd", "text": [{"text": ["no way"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9be", "text": [{"text": ["not now"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9bf", "text": [{"text": ["nah"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9c0", "text": [{"text": ["nay"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9c1", "text": [{"text": ["nope"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "65d72a6aa845250f3bd1b9c2", "text": [{"text": ["no"]}], "intentID": "6579c778dd3cca0fe0e0b152", "language": "en-us", "createdAt": "2025-01-18T14:18:08.000Z"}], "requiredEntities": [], "folders": [], "responses": [{"id": "678fc5923a5bd540470e71eb", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-21T16:04:35.000Z", "updatedAt": "2025-01-21T16:05:14.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488552", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488555", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488558", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c9000748855b", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c9000748855e", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488561", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488564", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488567", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c9000748856a", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c9000748856d", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488570", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488573", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488576", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c379f1d907c90007488579", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c37ac0d511d2d0bea0226a", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c59dd76f3f9b24e0b43c70", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c59efe6f3f9b24e0b43d49", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a1d26f3f9b24e0b43ecc", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a4de6f3f9b24e0b440e0", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a7806f3f9b24e0b44358", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a7be6f3f9b24e0b443aa", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a83d6f3f9b24e0b44477", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a7f36f3f9b24e0b44400", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a8396f3f9b24e0b4446a", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": true, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a83b6f3f9b24e0b4446d", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": true, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a8e86f3f9b24e0b44503", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5a92d6f3f9b24e0b44565", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f56427b27f078d3e6f7e", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f68627b27f078d3e7069", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f73227b27f078d3e70e4", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": true, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f73627b27f078d3e70f6", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f76a27b27f078d3e7122", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": true, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f77227b27f078d3e712b", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": true, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f77527b27f078d3e7137", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f9e927b27f078d3e7243", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c6179b7c730c1a5ae85045", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66cf35f093364975386e4223", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66cf365893364975386e425f", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66da131c5e1bae3341b56507", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66da14195e1bae3341b56583", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66da148d5e1bae3341b565db", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66da14fe5e1bae3341b56624", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ebf67f0b84ecf04fc94a", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5cd12f9fabfe28b707605", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5ccc17f0b84ecf05110ea", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5cde7f9fabfe28b707623", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5ce40f9fabfe28b707627", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66db0378d8e20c0b54aaec9b", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "678da96a4e984c4ee9b29b4c", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T01:39:54.000Z", "updatedAt": "2025-01-20T01:41:03.000Z", "updatedByID": 1464246}, {"id": "678dabd24e984c4ee9b29baa", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T01:50:11.000Z", "updatedAt": "2025-01-20T01:51:12.000Z", "updatedByID": 1464246}, {"id": "678daccd4e984c4ee9b29bca", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T01:54:22.000Z", "updatedAt": "2025-01-20T01:55:19.000Z", "updatedByID": 1464246}, {"id": "678dad624e984c4ee9b29be4", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T01:56:51.000Z", "updatedAt": "2025-01-20T01:57:13.000Z", "updatedByID": 1464246}, {"id": "678dadd54e984c4ee9b29bf9", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T01:58:46.000Z", "updatedAt": "2025-01-20T01:59:57.000Z", "updatedByID": 1464246}, {"id": "678db1b34e984c4ee9b29d7e", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T02:15:15.000Z", "updatedAt": "2025-01-20T02:17:00.000Z", "updatedByID": 1464246}, {"id": "678db2304e984c4ee9b29e0d", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T02:17:20.000Z", "updatedAt": "2025-01-20T02:17:51.000Z", "updatedByID": 1464246}, {"id": "678db2c24e984c4ee9b29ec0", "name": "", "createdByID": 1464246, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T02:19:46.000Z", "updatedAt": "2025-01-20T02:21:15.000Z", "updatedByID": 1464246}], "responseMessages": [{"id": "66c379f1d907c90007488554", "discriminatorID": "66c379f1d907c90007488553", "text": [{"text": ["Second Option"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488557", "discriminatorID": "66c379f1d907c90007488556", "text": [{"text": ["How can I help you?"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748855a", "discriminatorID": "66c379f1d907c90007488559", "text": [{"text": ["First Option"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748855d", "discriminatorID": "66c379f1d907c9000748855c", "text": [{"text": ["What is your name?"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488560", "discriminatorID": "66c379f1d907c9000748855f", "text": [{"text": ["Hello ", {"variableID": "name"}, " !"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488563", "discriminatorID": "66c379f1d907c90007488562", "text": [{"text": ["Twitter is the best 🐦"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488566", "discriminatorID": "66c379f1d907c90007488565", "text": [{"text": ["Error"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488569", "discriminatorID": "66c379f1d907c90007488568", "text": [{"text": ["This flow captures a users question and sends it to the OpenAI API"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748856c", "discriminatorID": "66c379f1d907c9000748856b", "text": [{"text": ["", {"variableID": "response"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488575", "discriminatorID": "66c379f1d907c90007488574", "text": [{"text": ["Payment was successful."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488578", "discriminatorID": "66c379f1d907c90007488577", "text": [{"text": ["<iframe src=\"", {"variableID": "66be69cd0d3bb525af0538ee"}, "\"  width=\"100%\" height=\"300\" style=\"border:none;\"></iframe>"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748857b", "discriminatorID": "66c379f1d907c9000748857a", "text": [{"text": ["payment failed."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c37ac0d511d2d0bea0226c", "discriminatorID": "66c37ac0d511d2d0bea0226b", "text": [{"text": ["Alright, kindly make payment on stripe and let me know when you are done, just say anything when you are here."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c59efe6f3f9b24e0b43d4b", "discriminatorID": "66c59efe6f3f9b24e0b43d4a", "text": [{"text": ["Kindly select a date."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7806f3f9b24e0b4435a", "discriminatorID": "66c5a7806f3f9b24e0b44359", "text": [{"text": ["Do you agree to these terms and would like to proceed with payment?"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c6179b7c730c1a5ae85047", "discriminatorID": "66c6179b7c730c1a5ae85046", "text": [{"text": ["Type \"Done\" after you have completed your payment."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748856f", "discriminatorID": "66c379f1d907c9000748856e", "text": [{"text": ["Hello Welcome to <PERSON><PERSON>, how may I be of help today?"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c59dd76f3f9b24e0b43c72", "discriminatorID": "66c59dd76f3f9b24e0b43c71", "text": [{"text": ["Oh great, kindly select your party size."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488572", "discriminatorID": "66c379f1d907c90007488571", "text": [{"text": ["\r\n<iframe width=\"300\" height=\"300\" frameborder=\"0\" style=\"border.0\" title=\"Voiceflow Stripe payment\" src=\"", {"text": [{"text": ["https://buy.stripe.com/test_6oE29Pculc2x08w7ss"], "attributes": {"color": {"a": 1, "b": 245, "g": 157, "r": 93}, "__type": "text", "underline": true}}], "attributes": {"url": "https://buy.stripe.com/test_6oE29Pculc2x08w7ss", "__type": "link"}}, "\" allowfullscreen>\r\n</iframe>"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a83d6f3f9b24e0b44479", "discriminatorID": "66c5a83d6f3f9b24e0b44478", "text": [{"text": ["Kind note that your reservation would not be booked without any deposit made."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a8396f3f9b24e0b4446c", "discriminatorID": "66c5a8396f3f9b24e0b4446b", "text": [""], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7f36f3f9b24e0b44402", "discriminatorID": "66c5a7f36f3f9b24e0b44401", "text": [{"text": ["Kindly let me know when you are done with the payment."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a83b6f3f9b24e0b4446f", "discriminatorID": "66c5a83b6f3f9b24e0b4446e", "text": [""], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a8e86f3f9b24e0b44505", "discriminatorID": "66c5a8e86f3f9b24e0b44504", "text": [{"text": ["would you like to continue with the payment?"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a92d6f3f9b24e0b44567", "discriminatorID": "66c5a92d6f3f9b24e0b44566", "text": [{"text": ["Alright thanks for checking out our service. Have a nice day."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f68627b27f078d3e706b", "discriminatorID": "66c5f68627b27f078d3e706a", "text": [{"text": ["", {"variableID": "66c5f6b527b27f078d3e7077"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f56427b27f078d3e6f80", "discriminatorID": "66c5f56427b27f078d3e6f7f", "text": [{"text": ["URL is ", {"variableID": "66be69cd0d3bb525af0538ee"}, " "]}, {"text": ["Session ID is: ", {"variableID": "66c5f54c27b27f078d3e6f62"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f73227b27f078d3e70e6", "discriminatorID": "66c5f73227b27f078d3e70e5", "text": [""], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f77527b27f078d3e7139", "discriminatorID": "66c5f77527b27f078d3e7138", "text": [{"text": ["Ooops, sorry your payment was not completed. Kindly try again."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f73627b27f078d3e70f8", "discriminatorID": "66c5f73627b27f078d3e70f7", "text": [{"text": ["Your payment has been successfully confirmed and you reservation has been made."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f76a27b27f078d3e7124", "discriminatorID": "66c5f76a27b27f078d3e7123", "text": [""], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f77227b27f078d3e712d", "discriminatorID": "66c5f77227b27f078d3e712c", "text": [""], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f9e927b27f078d3e7245", "discriminatorID": "66c5f9e927b27f078d3e7244", "text": [{"text": ["Payment process has been canceled"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66cf35f093364975386e4225", "discriminatorID": "66cf35f093364975386e4224", "text": [{"text": ["Oh great, kindly note that you are required to visit this chat again after you are done with your payment for confirmation purpose."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a4de6f3f9b24e0b440e2", "discriminatorID": "66c5a4de6f3f9b24e0b440e1", "text": [{"text": ["Kindly note that this booking needs a deposit of ", {"variableID": "66d5b6307f0b84ecf050ff38"}, " Pounds."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66db0378d8e20c0b54aaec9d", "discriminatorID": "66db0378d8e20c0b54aaec9c", "text": [{"text": ["Kindly let me know if you have any special request."]}, {"text": ["(For allergies, intolerances and dietary requirements, please speak to the restaurant directly.)"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7be6f3f9b24e0b443ac", "discriminatorID": "66c5a7be6f3f9b24e0b443ab", "text": [{"text": ["Hi welcome back ", {"variableID": "66d5ced37f0b84ecf0511245"}, ", type \"Done\" to confirm you have made the payment so I can proceed to confirm your reservation."]}, {"text": [{"text": [""]}]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5cd12f9fabfe28b707607", "discriminatorID": "66d5cd12f9fabfe28b707606", "text": [{"text": ["Alright ", {"variableID": "66d5ced37f0b84ecf0511245"}, ", may I have your last your last name please."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a1d26f3f9b24e0b43ece", "discriminatorID": "66c5a1d26f3f9b24e0b43ecd", "text": [{"text": ["Kindly select a time that works for you."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5ccc17f0b84ecf05110ec", "discriminatorID": "66d5ccc17f0b84ecf05110eb", "text": [{"text": ["Please provide your email address so we can send you a confirmation of your reservation."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5cde7f9fabfe28b707625", "discriminatorID": "66d5cde7f9fabfe28b707624", "text": [{"text": ["Alright may I have your first name please."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5ce40f9fabfe28b707629", "discriminatorID": "66d5ce40f9fabfe28b707628", "text": [{"text": ["Thank you ", {"variableID": "66d5ced37f0b84ecf0511245"}, ", and your mobile number please."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d2ebf67f0b84ecf04fc94c", "discriminatorID": "66d2ebf67f0b84ecf04fc94b", "text": [{"text": ["These are the available dates. Kindly select a date or \"Show More\" to view more dates."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da131c5e1bae3341b56509", "discriminatorID": "66da131c5e1bae3341b56508", "text": [{"text": ["Please select \"Make Payment\" to be directed to the payment page.  \r"]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66cf365893364975386e4261", "discriminatorID": "66cf365893364975386e4260", "text": [{"text": ["After making payment you will be redirected back here. Please type \"Done\" when you are back on this page."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da14195e1bae3341b56585", "discriminatorID": "66da14195e1bae3341b56584", "text": [{"text": ["Thank you for your reservation. We look forward to welcoming you at our restaurant."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da14fe5e1bae3341b56626", "discriminatorID": "66da14fe5e1bae3341b56625", "text": [{"text": ["Kindly select one of our outlets."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "678fc5923a5bd540470e71ed", "discriminatorID": "678fc5923a5bd540470e71ec", "text": [{"text": ["The provided OTP has expired, would you like to recieve another OTP?"]}], "condition": null, "delay": null, "createdAt": "2025-01-21T16:04:35.000Z"}, {"id": "66da148d5e1bae3341b565dd", "discriminatorID": "66da148d5e1bae3341b565dc", "text": [{"text": ["Your payment has been successfully confirmed and your reservation has been made."]}, {"text": ["A booking confirmation has been sent to your email with your reservation details."]}], "condition": null, "delay": null, "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "678da96a4e984c4ee9b29b4e", "discriminatorID": "678da96a4e984c4ee9b29b4d", "text": [{"text": ["Sorry to hear you want to cancel your reservation."]}, {"text": ["No worries, you can always book next time."]}, {"text": ["Kindly provide your booking code to help furhter."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T01:39:54.000Z"}, {"id": "678dabd24e984c4ee9b29bac", "discriminatorID": "678dabd24e984c4ee9b29bab", "text": [{"text": ["Thank you ", {"variableID": "66d5ced37f0b84ecf0511245"}, " for providing your booking code. "]}, {"text": ["I have your table reservation details as follows:"]}, {"text": ["1. Full name: ", {"variableID": "66d5ced37f0b84ecf0511245"}, " ", {"variableID": "66d5cf1d7f0b84ecf051124f"}, " "]}, {"text": ["2. Email address: ", {"variableID": "66d5cfa37f0b84ecf05112b1"}, " "]}, {"text": ["3. Reservation Date: ", {"variableID": "678d9e304e984c4ee9b29737"}, " "]}, {"text": ["4. <PERSON><PERSON>: ", {"variableID": "678d9e094e984c4ee9b2972a"}, " "]}, {"text": ["5. Party size: ", {"variableID": "66d5adc67f0b84ecf050faf0"}, " "]}, {"text": [{"text": [""]}]}, {"text": ["Is this the reservation you would like to cancel?"]}, {"text": [{"text": [""]}]}, {"text": [{"text": [""]}]}], "condition": null, "delay": null, "createdAt": "2025-01-20T01:50:11.000Z"}, {"id": "678daccd4e984c4ee9b29bcc", "discriminatorID": "678daccd4e984c4ee9b29bcb", "text": [{"text": ["Unfortunately, we could not find any reservation made with the booking code you provided. "]}, {"text": ["Please contact our support for assistance."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T01:54:22.000Z"}, {"id": "678dad624e984c4ee9b29be6", "discriminatorID": "678dad624e984c4ee9b29be5", "text": [{"text": ["Alright, that is duly noted. We have sent a one-time password (OTP) to your email. Please enter it below to complete your booking cancellation. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T01:56:51.000Z"}, {"id": "678dadd54e984c4ee9b29bfb", "discriminatorID": "678dadd54e984c4ee9b29bfa", "text": [{"text": ["Please contact our support assistance."]}, {"text": ["Have a nice day."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T01:58:46.000Z"}, {"id": "678db1b34e984c4ee9b29d80", "discriminatorID": "678db1b34e984c4ee9b29d7f", "text": [{"text": ["Unfortunately, we could not proceed with your request at this time. Kindly contact our support for assistance."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T02:15:15.000Z"}, {"id": "678db2304e984c4ee9b29e0f", "discriminatorID": "678db2304e984c4ee9b29e0e", "text": [{"text": ["The provided OTP is invalid, would you like to receive another OTP?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T02:17:20.000Z"}, {"id": "678db2c24e984c4ee9b29ec2", "discriminatorID": "678db2c24e984c4ee9b29ec1", "text": [{"text": ["Your booked reservation for ", {"variableID": "678d9e304e984c4ee9b29737"}, " at ", {"variableID": "678d9e094e984c4ee9b2972a"}, " has been successfully cancelled. An email has been sent you on the next steps to get your refund."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T02:19:46.000Z"}], "responseDiscriminators": [{"id": "678fc5923a5bd540470e71ec", "channel": "default", "language": "en-us", "responseID": "678fc5923a5bd540470e71eb", "variantOrder": ["678fc5923a5bd540470e71ed"], "createdAt": "2025-01-21T16:04:35.000Z"}, {"id": "66c379f1d907c90007488553", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488552", "variantOrder": ["66c379f1d907c90007488554"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488556", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488555", "variantOrder": ["66c379f1d907c90007488557"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488559", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488558", "variantOrder": ["66c379f1d907c9000748855a"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748855c", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c9000748855b", "variantOrder": ["66c379f1d907c9000748855d"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748855f", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c9000748855e", "variantOrder": ["66c379f1d907c90007488560"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488562", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488561", "variantOrder": ["66c379f1d907c90007488563"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488565", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488564", "variantOrder": ["66c379f1d907c90007488566"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488568", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488567", "variantOrder": ["66c379f1d907c90007488569"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748856b", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c9000748856a", "variantOrder": ["66c379f1d907c9000748856c"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748856e", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c9000748856d", "variantOrder": ["66c379f1d907c9000748856f"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488571", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488570", "variantOrder": ["66c379f1d907c90007488572"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488574", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488573", "variantOrder": ["66c379f1d907c90007488575"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c90007488577", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488576", "variantOrder": ["66c379f1d907c90007488578"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c379f1d907c9000748857a", "channel": "default", "language": "en-us", "responseID": "66c379f1d907c90007488579", "variantOrder": ["66c379f1d907c9000748857b"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c37ac0d511d2d0bea0226b", "channel": "default", "language": "en-us", "responseID": "66c37ac0d511d2d0bea0226a", "variantOrder": ["66c37ac0d511d2d0bea0226c"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c59dd76f3f9b24e0b43c71", "channel": "default", "language": "en-us", "responseID": "66c59dd76f3f9b24e0b43c70", "variantOrder": ["66c59dd76f3f9b24e0b43c72"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c59efe6f3f9b24e0b43d4a", "channel": "default", "language": "en-us", "responseID": "66c59efe6f3f9b24e0b43d49", "variantOrder": ["66c59efe6f3f9b24e0b43d4b"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a1d26f3f9b24e0b43ecd", "channel": "default", "language": "en-us", "responseID": "66c5a1d26f3f9b24e0b43ecc", "variantOrder": ["66c5a1d26f3f9b24e0b43ece"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a4de6f3f9b24e0b440e1", "channel": "default", "language": "en-us", "responseID": "66c5a4de6f3f9b24e0b440e0", "variantOrder": ["66c5a4de6f3f9b24e0b440e2"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7806f3f9b24e0b44359", "channel": "default", "language": "en-us", "responseID": "66c5a7806f3f9b24e0b44358", "variantOrder": ["66c5a7806f3f9b24e0b4435a"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7f36f3f9b24e0b44401", "channel": "default", "language": "en-us", "responseID": "66c5a7f36f3f9b24e0b44400", "variantOrder": ["66c5a7f36f3f9b24e0b44402"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a8396f3f9b24e0b4446b", "channel": "default", "language": "en-us", "responseID": "66c5a8396f3f9b24e0b4446a", "variantOrder": ["66c5a8396f3f9b24e0b4446c"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a83b6f3f9b24e0b4446e", "channel": "default", "language": "en-us", "responseID": "66c5a83b6f3f9b24e0b4446d", "variantOrder": ["66c5a83b6f3f9b24e0b4446f"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a7be6f3f9b24e0b443ab", "channel": "default", "language": "en-us", "responseID": "66c5a7be6f3f9b24e0b443aa", "variantOrder": ["66c5a7be6f3f9b24e0b443ac"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a83d6f3f9b24e0b44478", "channel": "default", "language": "en-us", "responseID": "66c5a83d6f3f9b24e0b44477", "variantOrder": ["66c5a83d6f3f9b24e0b44479"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a8e86f3f9b24e0b44504", "channel": "default", "language": "en-us", "responseID": "66c5a8e86f3f9b24e0b44503", "variantOrder": ["66c5a8e86f3f9b24e0b44505"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5a92d6f3f9b24e0b44566", "channel": "default", "language": "en-us", "responseID": "66c5a92d6f3f9b24e0b44565", "variantOrder": ["66c5a92d6f3f9b24e0b44567"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f76a27b27f078d3e7123", "channel": "default", "language": "en-us", "responseID": "66c5f76a27b27f078d3e7122", "variantOrder": ["66c5f76a27b27f078d3e7124"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f77227b27f078d3e712c", "channel": "default", "language": "en-us", "responseID": "66c5f77227b27f078d3e712b", "variantOrder": ["66c5f77227b27f078d3e712d"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f68627b27f078d3e706a", "channel": "default", "language": "en-us", "responseID": "66c5f68627b27f078d3e7069", "variantOrder": ["66c5f68627b27f078d3e706b"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f73227b27f078d3e70e5", "channel": "default", "language": "en-us", "responseID": "66c5f73227b27f078d3e70e4", "variantOrder": ["66c5f73227b27f078d3e70e6"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f73627b27f078d3e70f7", "channel": "default", "language": "en-us", "responseID": "66c5f73627b27f078d3e70f6", "variantOrder": ["66c5f73627b27f078d3e70f8"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f77527b27f078d3e7138", "channel": "default", "language": "en-us", "responseID": "66c5f77527b27f078d3e7137", "variantOrder": ["66c5f77527b27f078d3e7139"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f56427b27f078d3e6f7f", "channel": "default", "language": "en-us", "responseID": "66c5f56427b27f078d3e6f7e", "variantOrder": ["66c5f56427b27f078d3e6f80"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c5f9e927b27f078d3e7244", "channel": "default", "language": "en-us", "responseID": "66c5f9e927b27f078d3e7243", "variantOrder": ["66c5f9e927b27f078d3e7245"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66c6179b7c730c1a5ae85046", "channel": "default", "language": "en-us", "responseID": "66c6179b7c730c1a5ae85045", "variantOrder": ["66c6179b7c730c1a5ae85047"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d2ebf67f0b84ecf04fc94b", "channel": "default", "language": "en-us", "responseID": "66d2ebf67f0b84ecf04fc94a", "variantOrder": ["66d2ebf67f0b84ecf04fc94c"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66cf35f093364975386e4224", "channel": "default", "language": "en-us", "responseID": "66cf35f093364975386e4223", "variantOrder": ["66cf35f093364975386e4225"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66cf365893364975386e4260", "channel": "default", "language": "en-us", "responseID": "66cf365893364975386e425f", "variantOrder": ["66cf365893364975386e4261"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5cd12f9fabfe28b707606", "channel": "default", "language": "en-us", "responseID": "66d5cd12f9fabfe28b707605", "variantOrder": ["66d5cd12f9fabfe28b707607"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5ccc17f0b84ecf05110eb", "channel": "default", "language": "en-us", "responseID": "66d5ccc17f0b84ecf05110ea", "variantOrder": ["66d5ccc17f0b84ecf05110ec"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5cde7f9fabfe28b707624", "channel": "default", "language": "en-us", "responseID": "66d5cde7f9fabfe28b707623", "variantOrder": ["66d5cde7f9fabfe28b707625"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66d5ce40f9fabfe28b707628", "channel": "default", "language": "en-us", "responseID": "66d5ce40f9fabfe28b707627", "variantOrder": ["66d5ce40f9fabfe28b707629"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66db0378d8e20c0b54aaec9c", "channel": "default", "language": "en-us", "responseID": "66db0378d8e20c0b54aaec9b", "variantOrder": ["66db0378d8e20c0b54aaec9d"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da131c5e1bae3341b56508", "channel": "default", "language": "en-us", "responseID": "66da131c5e1bae3341b56507", "variantOrder": ["66da131c5e1bae3341b56509"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da14195e1bae3341b56584", "channel": "default", "language": "en-us", "responseID": "66da14195e1bae3341b56583", "variantOrder": ["66da14195e1bae3341b56585"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da148d5e1bae3341b565dc", "channel": "default", "language": "en-us", "responseID": "66da148d5e1bae3341b565db", "variantOrder": ["66da148d5e1bae3341b565dd"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "66da14fe5e1bae3341b56625", "channel": "default", "language": "en-us", "responseID": "66da14fe5e1bae3341b56624", "variantOrder": ["66da14fe5e1bae3341b56626"], "createdAt": "2025-01-18T14:18:08.000Z"}, {"id": "678db1b34e984c4ee9b29d7f", "channel": "default", "language": "en-us", "responseID": "678db1b34e984c4ee9b29d7e", "variantOrder": ["678db1b34e984c4ee9b29d80"], "createdAt": "2025-01-20T02:15:15.000Z"}, {"id": "678da96a4e984c4ee9b29b4d", "channel": "default", "language": "en-us", "responseID": "678da96a4e984c4ee9b29b4c", "variantOrder": ["678da96a4e984c4ee9b29b4e"], "createdAt": "2025-01-20T01:39:54.000Z"}, {"id": "678dabd24e984c4ee9b29bab", "channel": "default", "language": "en-us", "responseID": "678dabd24e984c4ee9b29baa", "variantOrder": ["678dabd24e984c4ee9b29bac"], "createdAt": "2025-01-20T01:50:11.000Z"}, {"id": "678db2304e984c4ee9b29e0e", "channel": "default", "language": "en-us", "responseID": "678db2304e984c4ee9b29e0d", "variantOrder": ["678db2304e984c4ee9b29e0f"], "createdAt": "2025-01-20T02:17:20.000Z"}, {"id": "678db2c24e984c4ee9b29ec1", "channel": "default", "language": "en-us", "responseID": "678db2c24e984c4ee9b29ec0", "variantOrder": ["678db2c24e984c4ee9b29ec2"], "createdAt": "2025-01-20T02:19:46.000Z"}, {"id": "678daccd4e984c4ee9b29bcb", "channel": "default", "language": "en-us", "responseID": "678daccd4e984c4ee9b29bca", "variantOrder": ["678daccd4e984c4ee9b29bcc"], "createdAt": "2025-01-20T01:54:22.000Z"}, {"id": "678dad624e984c4ee9b29be5", "channel": "default", "language": "en-us", "responseID": "678dad624e984c4ee9b29be4", "variantOrder": ["678dad624e984c4ee9b29be6"], "createdAt": "2025-01-20T01:56:51.000Z"}, {"id": "678dadd54e984c4ee9b29bfa", "channel": "default", "language": "en-us", "responseID": "678dadd54e984c4ee9b29bf9", "variantOrder": ["678dadd54e984c4ee9b29bfb"], "createdAt": "2025-01-20T01:58:46.000Z"}], "variables": [{"id": "66be69cd0d3bb525af0538ee", "name": "url", "createdByID": 1464246, "folderID": null, "color": "#5B9FD7", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "sessions", "name": "sessions", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "number", "description": "The number of times a particular user has opened the app.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "user_id", "name": "user_id", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The user's unique ID.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "timestamp", "name": "timestamp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "number", "description": "UNIX timestamp (number of seconds since January 1st, 1970 at UTC).", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "platform", "name": "platform", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The platform your agent is running on (e.g. \"voiceflow\").", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "intent_confidence", "name": "intent_confidence", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "number", "description": "The confidence interval (measured as a value from 0 to 100) for the most recently matched intent.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "last_response", "name": "last_response", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The agent's last response (text/speak) in a string.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "last_event", "name": "last_event", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "any", "description": "The object containing the last event that the user client has triggered.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "last_utterance", "name": "last_utterance", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The user's last utterance in a text string.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "vf_memory", "name": "vf_memory", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "Last 10 user inputs and agent responses in a string (e.g. \"agent: How can i help?\"\nuser: What's the weather today?).", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec617f0b84ecf04fc97f", "name": "firstDate", "createdByID": 1464246, "folderID": null, "color": "#5B9FD7", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec6d7f0b84ecf04fc980", "name": "secondDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec777f0b84ecf04fc981", "name": "thirdDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec7f7f0b84ecf04fc982", "name": "fourthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec8b7f0b84ecf04fc985", "name": "fifthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ec997f0b84ecf04fc991", "name": "sixthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2eca47f0b84ecf04fc9a6", "name": "seventhDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ecbb7f0b84ecf04fc9a9", "name": "eighthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ed067f0b84ecf04fc9ba", "name": "ninthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2ed117f0b84ecf04fc9bd", "name": "tenthDate", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d2f7957f0b84ecf04fcf06", "name": "showMore", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f54c27b27f078d3e6f62", "name": "stripe_session_id", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66c5f6b527b27f078d3e7077", "name": "vf_payment_status", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This variable holds the value of the stripe payment", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5adc67f0b84ecf050faf0", "name": "party_size", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This variable holds the value of the party size.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5b6307f0b84ecf050ff38", "name": "deposit_fee", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the fee to be deposited\n", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5c41e7f0b84ecf05108eb", "name": "amount", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the amount to be used in creating payment session from the API.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66da16985e1bae3341b5672c", "name": "outlet", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the Enish outlets", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5ced37f0b84ecf0511245", "name": "first_name", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user first name\n", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5cf1d7f0b84ecf051124f", "name": "last_name", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user last name\n", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5cfa37f0b84ecf05112b1", "name": "email_address", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user's email address", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66d5d4387f0b84ecf05115ff", "name": "mobile_number", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This represents the user's mobile number\n", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "66db0437d8e20c0b54aaed3a", "name": "special_request", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This variable represents any special request the customer has.", "defaultValue": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-18T14:18:08.000Z", "updatedByID": 1464246}, {"id": "678d9e094e984c4ee9b2972a", "name": "restaurant_location", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T00:51:22.000Z", "updatedAt": "2025-01-20T00:51:22.000Z", "updatedByID": 1464246}, {"id": "678d9e304e984c4ee9b29737", "name": "reservation_date", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T00:52:01.000Z", "updatedAt": "2025-01-20T00:52:01.000Z", "updatedByID": 1464246}, {"id": "678d9e594e984c4ee9b29741", "name": "number_of_guests", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T00:52:42.000Z", "updatedAt": "2025-01-20T00:52:42.000Z", "updatedByID": 1464246}, {"id": "678da9d74e984c4ee9b29b66", "name": "booking_code", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T01:41:43.000Z", "updatedAt": "2025-01-20T01:41:43.000Z", "updatedByID": 1464246}, {"id": "678daec64e984c4ee9b29c1e", "name": "provided_otp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "This is the OTP provided by the user", "defaultValue": null, "createdAt": "2025-01-20T02:02:47.000Z", "updatedAt": "2025-01-20T02:02:47.000Z", "updatedByID": 1464246}, {"id": "678daf494e984c4ee9b29c47", "name": "otp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T02:04:58.000Z", "updatedAt": "2025-01-20T02:04:58.000Z", "updatedByID": 1464246}, {"id": "678daf5e4e984c4ee9b29c49", "name": "otp_timestamp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T02:05:19.000Z", "updatedAt": "2025-01-20T02:05:19.000Z", "updatedByID": 1464246}, {"id": "678db02a4e984c4ee9b29c7c", "name": "current_timestamp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T02:08:42.000Z", "updatedAt": "2025-01-20T02:08:42.000Z", "updatedByID": 1464246}, {"id": "678db0484e984c4ee9b29c7f", "name": "otp_timestamp_epoch", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T02:09:13.000Z", "updatedAt": "2025-01-20T02:09:13.000Z", "updatedByID": 1464246}, {"id": "678db06d4e984c4ee9b29cb9", "name": "elapsed_timestamp", "createdByID": 1464246, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T02:09:49.000Z", "updatedAt": "2025-01-20T02:09:49.000Z", "updatedByID": 1464246}], "workflows": [{"id": "66b92af54d5e6200074ececf", "name": "Home", "createdByID": 1464246, "folderID": null, "status": null, "isStart": true, "diagramID": "64386e21bb106b044ea34b78", "assigneeID": null, "description": null, "createdAt": "2025-01-18T14:18:08.000Z", "updatedAt": "2025-01-21T16:06:05.000Z", "updatedByID": 1464246}], "attachments": [], "cardButtons": [], "prompts": [], "promptMessages": [], "events": [], "functions": [], "functionPaths": [], "functionVariables": [], "project": {"type": "chat", "name": "<PERSON>ish <PERSON>", "image": "", "teamID": "Vwjrx52XE5", "members": [], "privacy": "private", "platform": "webchat", "_version": 1.2, "linkType": "STRAIGHT", "creatorID": 1464246, "updatedBy": 1464246, "apiPrivacy": "private", "platformData": {"invocationName": "template project general"}, "customThemes": [], "aiAssistSettings": {"aiPlayground": true}, "_id": "678bb820c4618d115e6b0b97", "updatedAt": "2025-01-21T16:05:30.901Z", "devVersion": "678bb820c4618d115e6b0b98", "liveVersion": "678bb820c4618d115e6b0b99"}, "_version": "1.2", "secrets": [], "variableStates": []}