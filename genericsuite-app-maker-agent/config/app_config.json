{"APP_NAME": "GSAM", "APP_VERSION": "0.2.0", "MAKER_MAME": "<PERSON>", "APP_ICON": ":sparkles:", "APP_DESCRIPTION": "GenericSuite App Maker (GSAM) is a tool designed to streamline the app development process. It supports ideation, naming, presentation, AI models evaluation, configuration and code generation compatible with GenericSuite library.", "CONVERSATION_DB_PATH": "./db/conversations.json", "DEFAULT_PAGE": "home", "CODE_GENERATION_ENABLED": true, "TEXT_GENERATION_ENABLED": true, "VIDEO_GENERATION_ENABLED": true, "IMAGE_GENERATION_ENABLED": true, "CONVERSATION_TITLE_LENGTH": 50, "VIDEO_GALLERY_COLUMNS": 3, "IMAGE_GALLERY_COLUMNS": 3, "DEFAULT_SUGGESTIONS": {"s1": "Give me ideas to develop a web application about...", "s2": "Give me catchy names for a web application about...", "s3": "Give me a README file content for an application about...", "s4": "Give me the ReactJs code for a AI Assistant, using Shadcn/UI"}, "DYNAMIC_SUGGESTIONS": false, "AGENT_SYSTEM_PROMPT": "[agent_system_prompt.txt]", "SUGGESTIONS_PROMPT_SYSTEM": "[suggestions_text_system_prompt.txt]", "SUGGESTIONS_PROMPT_TEXT": "[suggestions_text_user_prompt.txt]", "SUGGESTIONS_PROMPT_SUFFIX": "[suggestions_suffix_user_prompt.txt]", "SUGGESTIONS_QTY": 4, "SUGGESTIONS_MODEL_REPLACEMENT": {"o1-mini": "gpt-4o-mini", "o1": "gpt-4o-mini", "o1-preview": "gpt-4o-mini"}, "SUGGESTIONS_DEFAULT_TIMEFRAME": "48 hours", "SUGGESTIONS_DEFAULT_APP_SUBJECT": "48-hour Hackathon. A great opportunity to solve a real-world challenge for AI founders on creating an MVP of your AI-powered product that can help solve real-world problems in your industry.", "SUGGESTIONS_DEFAULT_APP_TYPE": "Web", "IDEATION_DEFAULT_TIMEFRAME": "the timeframe specified by the user, or 48 hours if not specified", "IDEATION_DEFAULT_QTY": "the quantity specified by the user, or 10 if not specified", "REFINE_VIDEO_PROMPT_SYSTEM": "[refine_video_system_prompt.txt]", "REFINE_VIDEO_PROMPT_TEXT": "[refine_video_user_prompt.txt]", "REFINE_LLM_PROMPT_SYSTEM": "[refine_llm_system_prompt.txt]", "REFINE_LLM_PROMPT_TEXT": "[refine_llm_user_prompt.txt]", "LLM_PROVIDERS": {"openrouter": {"requirements": ["OPENROUTER_API_KEY"], "active": true}, "together_ai": {"requirements": ["TOGETHER_AI_API_KEY"], "active": true}, "openai": {"name": "OpenAI", "requirements": ["OPENAI_API_KEY"]}, "aimlapi": {"name": "AI/ML API", "requirements": ["AIMLAPI_API_KEY"], "active": true}, "groq": {"name": "Groq", "requirements": ["GROQ_API_KEY"], "active": true}, "ollama": {"name": "Ollama", "requirements": [], "active": false}, "rhymes": {"name": "Rhymes", "requirements": ["RHYMES_ARIA_API_KEY"], "active": true}, "nvidia": {"name": "Nvidia", "requirements": ["NVIDIA_API_KEY"], "active": true}, "huggingface": {"name": "Hugging Face", "requirements": ["HUGGINGFACE_API_KEY"], "active": true}, "xai": {"name": "xAI (Grok)", "requirements": ["XAI_API_KEY"], "active": true}}, "NO_SYSTEM_PROMPT_ALLOWED_PROVIDERS": ["nvidia"], "NO_SYSTEM_PROMPT_ALLOWED_MODELS": ["o1-preview", "o1-mini", "o1"], "LLM_MODEL_PARAMS_NAMING": {"o1": [["max_tokens", "max_completion_tokens"]], "o1-preview": [["max_tokens", "max_completion_tokens"]], "o1-mini": [["max_tokens", "max_completion_tokens"]]}, "LLM_MODEL_FORCED_VALUES": {"o1": {"temperature": 1}, "o1-preview": {"temperature": 1}, "o1-mini": {"temperature": 1}}, "LLM_AVAILABLE_MODELS": {"openrouter": ["google/gemini-2.0-flash-exp:free", "google/gemini-2.0-flash-thinking-exp:free", "deepseek/deepseek-r1:free", "meta-llama/llama-3.2-3b-instruct:free", "qwen/qwen-2-7b-instruct:free"], "openai": ["gpt-4o", "gpt-4o-mini", "o1-mini", "o1", "o1-preview", "gpt-4"], "groq": ["llama3-groq-70b-8192-tool-use-preview", "llama3-8b-8192", "mixtral-8x7b-32768"], "ollama": ["llama3.2", "llava", "deepseek-coder-v2", "nemotron"], "rhymes": ["aria"], "nvidia": ["nvidia/llama-3.1-nemotron-70b-instruct"], "huggingface": ["meta-llama/Meta-Llama-3.1-8B-Instruct", "mistralai/Mistral-7B-Instruct-v0.2"], "together_ai": ["meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", "meta-llama/Llama-3.2-3B-Instruct-Turbo", "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo", "deepseek-ai/DeepSeek-V3"], "xai": ["grok-beta"], "aimlapi": ["meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo", "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "codellama/CodeLlama-70b-Python-hf", "codellama/CodeLlama-34b-Python-hf", "codellama/CodeLlama-13b-Python-hf", "codellama/CodeLlama-7b-Python-hf", "codellama/CodeLlama-70b-Instruct-hf", "codellama/CodeLlama-34b-Instruct-hf", "codellama/CodeLlama-13b-Instruct-hf", "codellama/CodeLlama-7b-Instruct-hf", "google/gemma-7b-it", "google/gemma-2b-it", "mistralai/Mixtral-8x7B-Instruct-v0.1", "mistralai/Mistral-7B-Instruct-v0.2", "NousResearch/Nous-Capybara-7B-V1p9", "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO", "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT", "NousResearch/Nous-Hermes-llama-2-7b", "NousResearch/Nous-Hermes-Llama2-13b", "NousResearch/Nous-Hermes-2-Yi-34B", "Qwen/Qwen1.5-0.5B-Cha<PERSON>", "Qwen/Qwen1.5-1.8B-Cha<PERSON>", "Qwen/Qwen1.5-4B-<PERSON><PERSON>", "Qwen/Qwen1.5-7B-<PERSON><PERSON>", "Qwen/Qwen1.5-14B-<PERSON><PERSON>", "Qwen/Qwen1.5-72B-<PERSON><PERSON>", "togethercomputer/falcon-40b-instruct", "togethercomputer/falcon-7b-instruct", "gpt-4o", "gpt-4o-mini", "o1-mini", "o1-preview", "gpt-4", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]}, "TEXT_TO_IMAGE_PROVIDERS": {"huggingface": {"requirements": ["HUGGINGFACE_API_KEY"], "active": true}, "openai": {"requirements": ["OPENAI_API_KEY"], "active": true}}, "TEXT_TO_IMAGE_AVAILABLE_MODELS": {"openai": ["dall-e-3", "gpt-4o"], "huggingface": ["black-forest-labs/FLUX.1-dev", "black-forest-labs/FLUX.1-schnell"]}, "TEXT_TO_VIDEO_PROVIDERS": {"rhymes": {"requirements": ["RHYMES_ALLEGRO_API_KEY"], "active": true, "require_follow_up": true}}, "TEXT_TO_VIDEO_AVAILABLE_MODELS": {"rhymes": ["allegro"]}, "START_APP_CODE_ENABLED": false, "ADD_ATTACHMENTS_ENABLED": false, "GENERATE_VIDEO_SCRIPT_ENABLED": false}