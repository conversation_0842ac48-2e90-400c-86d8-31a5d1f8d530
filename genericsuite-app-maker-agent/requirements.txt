aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anthropic==0.45.2
anyio==4.8.0
asyncpg==0.30.0
attrs==25.1.0
beautifulsoup4==4.12.3
blinker==1.9.0
cachetools==5.5.1
certifi==2024.12.14
charset-normalizer==3.4.1
click==8.1.8
cohere==5.13.11
colorama==0.4.6
dataclasses-json==0.6.7
Deprecated==1.2.18
deprecation==2.1.0
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.7.0
eval_type_backport==0.2.2
executing==2.2.0
fastapi==0.115.7
fastavro==1.10.0
filelock==3.17.0
filetype==1.2.0
frozenlist==1.5.0
fsspec==2024.12.0
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.38.0
googleapis-common-protos==1.66.0
gotrue==2.11.2
greenlet==3.1.1
griffe==1.5.5
groq==0.15.0
h11==0.14.0
h2==4.1.0
hpack==4.1.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.28.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.5.0
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
jsonpath-python==1.0.6
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
llama-cloud==0.1.11
llama-index==0.12.14
llama-index-agent-openai==0.4.3
llama-index-cli==0.4.0
llama-index-core==0.12.14
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.4
llama-index-llms-openai==0.3.14
llama-index-multi-modal-llms-openai==0.4.2
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.4
llama-index-readers-llama-parse==0.4.0
llama-parse==0.5.20
logfire==3.4.0
logfire-api==3.4.0
lxml==5.3.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.0
mdurl==0.1.2
mistralai==1.5.0
multidict==6.1.0
mypy-extensions==1.0.0
narwhals==1.24.1
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.2
ollama==0.4.7
openai==1.60.2
opentelemetry-api==1.29.0
opentelemetry-exporter-otlp-proto-common==1.29.0
opentelemetry-exporter-otlp-proto-http==1.29.0
opentelemetry-instrumentation==0.50b0
opentelemetry-proto==1.29.0
opentelemetry-sdk==1.29.0
opentelemetry-semantic-conventions==0.50b0
packaging==24.2
pandas==2.2.3
pillow==10.4.0
postgrest==0.19.3
propcache==0.2.1
protobuf==5.29.3
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.6
pydantic-ai==0.0.20
pydantic-ai-slim==0.0.20
pydantic-graph==0.0.20
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
pymongo==4.11
pypdf==5.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-pptx==1.0.2
pytz==2024.2
PyYAML==6.0.2
realtime==2.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rpds-py==0.22.3
rsa==4.9
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.37
starlette==0.45.3
storage3==0.11.1
streamlit==1.41.1
StrEnum==0.4.15
striprtf==0.0.26
supabase==2.12.0
supafunc==0.9.2
tabulate==0.9.0
tenacity==9.0.0
tiktoken==0.8.0
together==1.3.14
tokenizers==0.21.0
toml==0.10.2
tornado==6.4.2
tqdm==4.67.1
typer==0.15.1
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uvicorn==0.34.0
websockets==13.1
wrapt==1.17.2
XlsxWriter==3.2.2
yarl==1.18.3
zipp==3.21.0
