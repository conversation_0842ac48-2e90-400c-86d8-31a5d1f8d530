# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# For the Postgres version (sample_postgres_agent.py), set your database connection URL.
# Format: postgresql://[user]:[password]@[host]:[port]/[database_name]
# Example: postgresql://postgres:mypassword@localhost:5432/mydb
# For Supabase Postgres connection, you can find this in Database settings -> Connection string -> URI
DATABASE_URL=

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN=

# Default LLM provider
# DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_PROVIDER=openrouter

# OpenRouter configuration
OPENROUTER_API_KEY=
OPENROUTER_MODEL_NAME=google/gemini-2.0-flash-exp:free
