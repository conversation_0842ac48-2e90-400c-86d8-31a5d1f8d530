# YouTube Learning AI Agent

Author: [<PERSON>](https://www.linkedin.com/in/david-zhu-704579248/)

**Platform:** n8n (you can import the .json file into your own n8n to check out the flow)

**Note:** All API keys have been removed from the .json file

This agent takes a YouTube link as input and generates a fill-in-the-blank note sheet, a quiz to take at the end of the video, and additional resources. It also provides a PDF file link containing all the generated content. (Note: The PDF may appear slightly off due to markdown translation issues.)

## Features

- Creates a fill-in-the-blank note sheet from any YouTube video  
- Generates a quiz to reinforce learning after watching  
- Offers supplementary resources for further study  
- Compiles all outputs into a single PDF file for easy distribution  

## How It Works

1. Receives a YouTube link from the user  
2. Processes the video content to generate notes, a quiz, and resources  
3. Formats and compiles the materials into PDF format  
4. Delivers the PDF link along with quick-view resources in real time  

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.


