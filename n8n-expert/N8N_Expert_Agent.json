{"name": "N8N Expert", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "f4a396a1-0e77-4ca8-9150-d27c4af2ee77", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [640, 640]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "3f7da8d5-e9d3-4c0b-9648-f23cdfb0dbfa", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1160, 820], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "text", "renameField": true, "outputFieldName": "workflow_ids"}]}, "options": {}}, "id": "aad41c91-c6d6-48c3-9e43-346a5069c667", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1720, 640]}, {"parameters": {"model": "gpt-4o", "options": {}}, "id": "cf371ddb-1521-4e9c-813c-5e5c7fdbce44", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1480, 560], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"operation": "getAll", "tableId": "workflows", "limit": 5, "filterType": "string", "filterString": "=workflow_id=in.{{ $json.query_string }}"}, "id": "16abcadb-db84-4be0-821d-725cd53ac196", "name": "Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2340, 400], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "workflow_ids"}]}, "options": {}}, "id": "1e00bcc2-ea78-4b35-8c81-2ee625c7d5a2", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1940, 400]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.query_string = $('Summarize').first().json.concatenated_workflow_ids.replaceAll(\"\\\"\", \"\").replaceAll(\"[\", \"(\").replaceAll(\"]\", \")\");\n}\n\nreturn $input.all();"}, "id": "64627fc4-d2e5-42d4-a0b5-3bcd931d24ab", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2140, 640]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "output", "value": "={{ $('Aggregate1').item.json.data.length ? \"Here are the recommended workflows to use as an example for you:\" : \"No related workflows found at this time. Please try a different prompt.\" }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "4a14b34b-ecb6-4d43-836c-ceec97f626cd", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2720, 420]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Edit Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json.output,\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "b9f99ec8-96df-45ff-9d41-c78328889a1e", "name": "Supabase1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2920, 640], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "df095f3f-9691-419e-aca6-c43eabe14de8", "name": "Aggregate1", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2520, 640]}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "59550b57-845f-49ef-80c8-b14eae1d2d5a", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [3320, 660]}, {"parameters": {"promptType": "define", "text": "=Here is a n8n workflow descriptions:\n\n{{ $json.document.pageContent }}\n\nYour job is to identify if the workflow is relevant to the user query which is:\n\n{{ $('Edit Fields').first().json.query }}\n\nOutput the workflow ID if the workflow relates to the user query. Otherwise, output -1. ONLY ever output a number, nothing else. You will always output the workflow ID or -1.\n\nThe workflow ID is: {{ $json.document.metadata.workflow_id }}\n\nThe workflow ID or -1 if the workflow doesn't relate to the user query:"}, "id": "4c49b44d-b707-41ef-8577-4939df2bc2db", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.4, "position": [1360, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "output", "value": "={{ $('Edit Fields1').item.json.output }}", "type": "string"}, {"id": "392160a6-53a6-4b7e-9c43-75571172c590", "name": "data", "value": "={{ $('Edit Fields1').item.json.data}}", "type": "string"}]}, "options": {}}, "id": "416ee827-a9a5-4461-8b77-798b7ecaea50", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [3120, 420]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "********-cbdb-4c90-9dc6-adbcb516f6de", "name": "Supabase2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [840, 420], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"mode": "load", "tableName": {"__rl": true, "value": "workflows", "mode": "list", "cachedResultName": "workflows"}, "prompt": "={{ $('Edit Fields').item.json.query }}", "topK": 5, "options": {"queryName": "match_summaries"}}, "id": "ee10e7a4-da98-4775-b641-310ce79110a4", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1020, 640], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"content": "## The n8n Expert Agent helps you find and understand n8n automation workflows. Simply describe what you're trying to automate, and the agent will recommend relevant workflows to help you get started.\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\n## For example:\n\n### User: \"I need to automatically post tweets when new blog posts are published\"\n### Agent: Here are some recommended workflows to help you get started:\n- WordPress to Twitter Auto-Poster\n- Blog RSS Feed to Social Media\n- Content Distribution Workflow\nThe agent analyzes your request and provides matching workflows from its knowledge base, along with explanations of how to implement and customize them for your needs.", "height": 502.************, "width": 651.*************, "color": 6}, "id": "1ac32a2f-f121-4b7c-bfb6-b90056c9d6bd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-260, 360]}, {"parameters": {"httpMethod": "POST", "path": "invoke-n8n-expert", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "90236e22-385a-49ba-8d5c-263a60a82f33", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [460, 460], "webhookId": "ba5e9235-01af-47f8-9f72-29df862bfa0f", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "#### This agent is in beta, especially as its knowledgebase grows! Workflows won't always be super related to your query and sometimes it won't have any. This agent will also involve into a conversational agent eventually to help you build n8n workflows!", "height": 111.**************, "width": 648.*************}, "id": "934e70b5-3825-4bb7-91e3-0fa786bf88e8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-260, 900]}], "pinData": {}, "connections": {"Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Supabase": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}]]}, "Supabase1": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "9cb778c5-1d8e-4af0-a6fd-8107eb8069ef", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "e1rZ3MNMH9g4i1Yw", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}