Converts content between different formats. Transforms input content from any supported format into the specified output format.
Supported formats:
- Basic formats: txt, html, markdown
- Advanced formats (REQUIRE complete file paths): pdf, docx, rst, latex, epub

CORRECT Usage Examples:
1. 'Convert this text to HTML' (basic conversion)
   - <PERSON><PERSON> will show converted content

2. 'Save this text as PDF at /documents/story.pdf'
   - Correct: specifies path + filename + extension
   - <PERSON><PERSON> will show: 'Content successfully converted and saved to: /documents/story.pdf'

INCORRECT Usage:
1. 'Save this as PDF in /documents/'
   -Missing filename and extension
2. 'Convert to PDF'
   - Missing complete file path

When requesting conversion, ALWAYS specify:
1. The content or input file
2. The desired output format
3. For advanced formats: complete output path + filename + extension
Example: 'Convert this markdown to PDF and save as /path/to/output.pdf'

Always check the success message for the exact file location.