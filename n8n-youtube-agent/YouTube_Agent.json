{"name": "YouTube Agent", "nodes": [{"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "04206a25-8d06-4b73-9fd7-e3b637a78a33", "name": "Add User Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1220, 1780], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Add User Message to DB').isExecuted && $('Add User Message to DB').last().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json.data,\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "3e026f8c-6d06-4065-8173-0f086d8adbcd", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2640, 2320], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "********-a3d2-43ac-a0af-e1b9b6c7f266", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1480, 1980]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}, {"id": "17e06634-66c7-41d2-8324-2d2c72da762b", "name": "message.content", "value": "={{ $json.message.content }}", "type": "string"}]}, "options": {}}, "id": "1b509e51-c63e-4e5b-b432-48447ea97d71", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2940, 2320]}, {"parameters": {"httpMethod": "POST", "path": "invoke-youtube-agent", "authentication": "headerAuth", "responseMode": "=responseNode", "options": {"allowedOrigins": "*"}}, "id": "bf2496b1-e2db-4a46-8c68-26d713554f7d", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1720, 1980], "webhookId": "9a6c4630-b422-4d42-b894-81ecfe881ffe", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.message.content }}", "hasOutputParser": true, "options": {"systemMessage": "=You are a classifier for a YouTube video interaction system. Your role is to analyze user input and classify it into structured actions related to YouTube videos. You must respond ONLY with a JSON object representing the classified action.\n\nOutput Format:\n{\n    \"action_type\": \"add|summarize|search|respond\",\n    \"video_url\": \"<url or null>\",\n    \"video_title\": \"<title or null>\",\n    \"search_query\": \"<search terms or null>\",\n    \"confidence\": <0.0-1.0>,\n    \"response\": \"response\"\n}\n\nRules:\n- If a user provides a YouTube URL alone, classify it as an \"add\" action\n- If a user asks about video content with a URL or title, classify it as a \"summarize\" action\n- If a user asks to find specific information from past videos, classify it as a \"search\" action\n- Include the confidence score based on how clearly the user's intent matches the classification\n- Only include non-null values for relevant fields\n- If you can respond directly, answer with a action_type of 'respond' and set the \"response\" property. IT IS IMPORTANT THAT YOU RETURN A RESPONSE FOR THE USER IN THIS CASE!\n\nExamples:\n\n1. User Input: \"https://youtube.com/watch?v=12345\"\nOutput:\n{\n    \"action_type\": \"add\",\n    \"video_url\": \"https://youtube.com/watch?v=12345\",\n    \"confidence\": 1.0\n}\n\n2. User Input: \"Can you please add this video https://youtube.com/watch?v=67890\"\nOutput:\n{\n    \"action_type\": \"add\",\n    \"video_url\": \"https://youtube.com/watch?v=67890\",\n    \"confidence\": 1.0\n}\n\n3. User Input: \"Can you summarize this video for me: https://youtube.com/watch?v=24680\"\nOutput:\n{\n    \"action_type\": \"summarize\",\n    \"video_url\": \"https://youtube.com/watch?v=24680\",\n    \"confidence\": 1.0\n}\n\n4. User Input: \"What was that video about neural networks that I watched last week?\"\nOutput:\n{\n    \"action_type\": \"search\",\n    \"search_query\": \"neural networks\",\n    \"confidence\": 0.8\n}\n\n5. User Input: \"Please summarize the video 'Getting Started with Python'\"\nOutput:\n{\n    \"action_type\": \"summarize\",\n    \"video_title\": \"Getting Started with Python\",\n    \"confidence\": 0.9\n}\n\n6. User Input: \"I remember watching a video about startup ideas, can you find it?\"\nOutput:\n{\n    \"action_type\": \"search\",\n    \"search_query\": \"startup ideas\",\n    \"confidence\": 0.7\n}\n\n7. User Input: \"What do you think about this video https://youtube.com/watch?v=13579 and can you add it?\"\nOutput:\n{\n    \"action_type\": \"add\",\n    \"video_url\": \"https://youtube.com/watch?v=13579\",\n    \"confidence\": 0.8\n}\n\n8. User Input: \"What can you you?\"\nOutput:\nOutput:\n{\n    \"action_type\": \"respond\",\n    \"response\": \"I'm a youtube assitant and I can add youtube video to my database, answer questions about a specific videos, or can search by tags, titles, or topics that were discussed in videos. \n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-1040, 1580], "id": "ddb90334-566d-4126-9ab3-7f8509451e20", "name": "AI Agent"}, {"parameters": {"model": "claude-3-5-haiku-********", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [-1060, 960], "id": "211f2fc3-4536-45c5-af4c-0249ee429827", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "AiDvkdxUFBeRQmnE", "name": "Anthropic account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.output.action_type }}", "rightValue": "add", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "275e8dea-2a5d-4153-a58e-21f7ce644c4d", "leftValue": "={{ $json.output.action_type }}", "rightValue": "respond", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "75fb8c9b-bc0a-4f07-b6fa-2064cfb89941", "leftValue": "={{ $json.output.action_type }}", "rightValue": "summarize", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0a88cea3-d70d-4799-aa6d-1499d0d11c88", "leftValue": "={{ $json.output.action_type }}", "rightValue": "search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-660, 1580], "id": "c9da7f1c-4d5f-4ed4-a43b-30ff6f0de1af", "name": "Switch"}, {"parameters": {"jsonSchemaExample": "{\n    \"action_type\": \"add|summarize|search\",\n    \"video_url\": \"url or null\",\n    \"video_title\": \"title or null\",\n    \"search_query\": \"search terms or null\",\n    \"confidence\": 0.0,\n  \"response\": \"Test\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-860, 1800], "id": "2f37a37a-76fb-41c4-a320-e83541be3ebb", "name": "Structured Output Parser"}, {"parameters": {"jsCode": "function getYouTubeVideoId(url) {\n  // Handle different URL patterns\n  const patterns = [\n    /(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([^&?/]+)/,\n    /youtube\\.com\\/v\\/([^&?/]+)/,\n    /youtube\\.com\\/shorts\\/([^&?/]+)/\n  ];\n\n  for (const pattern of patterns) {\n    const match = url.match(pattern);\n    if (match) {\n      return match[1];\n    }\n  }\n\n  return null;\n}\nreturn { json: { videoId: getYouTubeVideoId($input.first().json.output.video_url) } }"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 1340], "id": "b2aa2686-12db-4717-836c-680d22bca2db", "name": "Code"}, {"parameters": {"promptType": "define", "text": "={{ $json.transcript }}", "hasOutputParser": true, "options": {"systemMessage": "=You are an expert summerizer for youtube video transcripts. \n\nPlease generate:\n- Summary\n- Key Points\n- Actionable tasks\n- Exacted quotes\n\nAlways only return the value as a json object:\n\nEXAMPLE:\n{\n  \"summary\": \"this is the summary\",\n  \"keypoints\": [\"point1\", \"point2\"],\n  \"actionable-tasks\": [\"task1\", \"task2\"],\n  \"extracted-quotes\": [\"quote 1, quote 2\"]\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1240, 1120], "id": "eecbb1bb-b22a-4d3e-a7ea-1269f2f3bfdf", "name": "Summarize agent"}, {"parameters": {"promptType": "define", "text": "={{ $json.transcript }}", "hasOutputParser": true, "options": {"systemMessage": "=You are an expert in generating tags for youtube videos to classify them, and allow users to search for them later\n\nPlease generate at least 10 tags but as many as you feel required.\n\nExample output:\n{ \"tags\": [ \"tag1\", \"tag2\" ] }\n\nIMPORTANT:\nOnly return the json result, nothing else!\nDO NOT respond with 'I'll generate tags ...'\nJust return the json!"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1220, 1440], "id": "03a4c74b-d80e-4021-bf84-9ef066431d52", "name": "Tag generator agent"}, {"parameters": {"jsonSchemaExample": "{ \"tags\": [ \"tag\"] }"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1340, 1700], "id": "0476abbd-ff59-4c4b-8a62-af7225b27fd8", "name": "Structured Output Parser1"}, {"parameters": {"jsonSchemaExample": "{\n  \"summary\": \"this is the summary\",\n  \"keypoints\": [\"point1\", \"point2\"],\n  \"actionable-tasks\": [\"task1\", \"task2\"],\n  \"extracted-quotes\": [\"quote 1, quote 2\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1400, 940], "id": "60715137-d876-4de1-b511-336aeca619cb", "name": "Structured Output Parser2"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1560, 1300], "id": "cba4c51b-58fa-4c77-9ccb-d7215bd655e1", "name": "<PERSON><PERSON>"}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": ""}]}}}, "id": "a43883b5-9a8c-42c5-91d2-bf14a82c0255", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [3200, 2320]}, {"parameters": {"tableId": "videos", "fieldsUi": {"fieldValues": [{"fieldId": "video_id", "fieldValue": "={{ $('Code').item.json.videoId }}"}, {"fieldId": "title", "fieldValue": "={{ $('Extract video details').first().json.title }}"}, {"fieldId": "channel", "fieldValue": "={{ $('Extract video details').first().json.channelName }}"}, {"fieldId": "description", "fieldValue": "={{ $('Extract video details').first().json.description }}"}, {"fieldId": "summary", "fieldValue": "={{ $('Merge').first().json.output.summary }}"}, {"fieldId": "keypoints", "fieldValue": "={{ $('Merge').first().json.output.keypoints }}"}, {"fieldId": "actionable", "fieldValue": "={{ $('Merge').first().json.output['actionable-tasks'] }}"}, {"fieldId": "quotes", "fieldValue": "={{ $json.output['extracted-quotes'] }}"}, {"fieldId": "transcript", "fieldValue": "={{ $('Join transcript in code1').first().json.transcript }}"}, {"fieldId": "tags", "fieldValue": "={{ $('Merge').last().json.output.tags }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1740, 1320], "id": "07bb6887-7ca4-44f7-90ec-57cc2928f8e6", "name": "Add video to supabase", "alwaysOutputData": false, "notesInFlow": false, "executeOnce": true, "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"url": "=https://[Your Supabase Project ID].supabase.co/rest/v1/videos?video_id=eq.{{ $json.videoId }}&select=id&limit=1", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-280, 1340], "id": "d5187c0b-7115-44c9-a825-6925b60dc7d5", "name": "Check if video exists", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "********-f8ad-4486-b8dc-a768d11403c9", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "number", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-120, 1300], "id": "64a39e6a-ffa5-45cc-9ad8-8ddc7301a5e6", "name": "If row exists"}, {"parameters": {"jsCode": "const videoId = $('Code').first().json.videoId;\nreturn {data: `The video with the id ${videoId} is already in the database.`}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [60, 1800], "id": "5e755949-209e-426c-b03c-356c837e1613", "name": "Return text"}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "youtube_agent_data", "mode": "list", "cachedResultName": "youtube_agent_data"}, "options": {"queryName": "match_youtube_data"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [2100, 1320], "id": "a8e5afd6-a473-4e29-a4f9-8a4a59b0c3c2", "name": "Insert documents", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-1040, 720], "id": "a14fd219-a27b-43b5-b814-638780df3a5a", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "953af65b-868c-4a76-a440-5102f2eada82", "name": "data", "value": "={{ $json.transcript }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1920, 1320], "id": "dee740ba-5f7b-4348-a43b-96be2d0e7554", "name": "<PERSON>"}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data }}", "options": {"metadata": {"metadataValues": [{"name": "video_id", "value": "={{ $('Code').item.json.videoId }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2000, 1000], "id": "91c9f00d-a961-42f1-a3d5-0ffc773608b9", "name": "Default Data Loader"}, {"parameters": {"chunkOverlap": 100, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [2360, 980], "id": "ed543a10-f937-4619-b3e3-68021e4000cc", "name": "Recursive Character Text Splitter"}, {"parameters": {"jsCode": "const response = $input.first().json.output.response\nreturn {data: `${response}`}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [60, 2320], "id": "d5f77a8c-6838-490a-9446-6ee9745bc84d", "name": "Return text2"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1700, 1720], "id": "468a2308-b48d-4da8-9dd1-c546db4e296e", "name": "When chat message received", "webhookId": "a023c538-05ea-4324-9c6d-44653d9a44d4"}, {"parameters": {"assignments": {"assignments": [{"id": "7c3b6c67-035c-4d27-ae67-51a9b68a8609", "name": "session_id", "value": "={{ $json.sessionId }}", "type": "string"}, {"id": "3838a84f-ec76-4c2f-a29a-5ed2775566d4", "name": "query", "value": "={{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1480, 1720], "id": "a3b95059-39f5-42ce-aa00-33cf73c15c9d", "name": "Edit Fields1"}, {"parameters": {"content": "# Summarize \n", "height": 483, "width": 1470, "color": 2}, "id": "387806a0-0604-43ce-866d-14176e416e91", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, 2700]}, {"parameters": {"content": "# Add", "height": 1263, "width": 2970, "color": 4}, "id": "98bc573a-1f0d-4ee8-9b5f-2a2b8aa10730", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, 840]}, {"parameters": {"operation": "get", "tableId": "videos", "filters": {"conditions": [{"keyName": "video_id", "keyValue": "={{ $json.videoId }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [300, 2840], "id": "68d1ea49-dd11-4ac6-8dea-4778e1c9b4de", "name": "Supabase", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"jsCode": "function getYouTubeVideoId(url) {\n  // Handle different URL patterns\n  const patterns = [\n    /(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([^&?/]+)/,\n    /youtube\\.com\\/v\\/([^&?/]+)/,\n    /youtube\\.com\\/shorts\\/([^&?/]+)/\n  ];\n\n  for (const pattern of patterns) {\n    const match = url.match(pattern);\n    if (match) {\n      return match[1];\n    }\n  }\n\n  return null;\n}\nreturn { json: { videoId: getYouTubeVideoId($input.first().json.output.video_url) } }"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [40, 2840], "id": "97247df0-2ada-4e34-aa56-516ddf9d54cd", "name": "Code1"}, {"parameters": {"assignments": {"assignments": [{"id": "b70b8806-7694-4be8-aed3-7d7a1b87d1fb", "name": "description", "value": "={{ $json.description }}", "type": "string"}, {"id": "dbb6ae07-9d8d-4327-9763-875d0a3921b1", "name": "summary", "value": "={{ $json.summary }}", "type": "string"}, {"id": "0face230-596b-4b54-b66a-7bd653f565c3", "name": "keypoints", "value": "={{ $json.keypoints }}", "type": "array"}, {"id": "39f5b157-b642-4508-a0c8-dcf1b62484b2", "name": "actionable", "value": "={{ $json['actionable'] }}", "type": "array"}, {"id": "ca2b44ef-d787-4996-90c8-e82cb677471d", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6da635e-747d-4e8b-9856-96a837aa3b9d", "name": "channel", "value": "={{ $json.channel }}", "type": "string"}, {"id": "35d0f345-cd4c-4aca-b9d8-b040c839bb74", "name": "query", "value": "={{ $('Prep Input Fields').item.json.query }}", "type": "string"}, {"id": "3c935f21-9401-4162-bf1a-53b4ba4cff51", "name": "videoId", "value": "={{ $json.video_id }}", "type": "string"}, {"id": "e08c77c9-0905-4038-8628-1083336a2044", "name": "session_id", "value": "={{ $('Prep Input Fields').item.json.session+_id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [540, 2840], "id": "736f3700-0deb-488b-b256-8929cfa692f1", "name": "Gather fields"}, {"parameters": {"promptType": "define", "text": "=You are an expert in answering a user question to a video summary. \nThe video is already summerized, and you get the following fields:\n\n- Title\n- Description\n- Summary\n- Key points\n- Tasks\n- Quotes\n\n# The video title is:\n<title>{{ $json.title }}</title>\n\n# The video channel is:\n<channel>{{ $json.channel }}</channel>\n\n# The video description is:\n<description>{{ $json.description }}</description>\n\n# The video Summary is:\n<summary>{{ $json.summary }}</summary>\n\n# Key points:\n<key-points>{{ $json.keypoints }}</key-points>\n\n# Tasks: \n<tasks>{{ $json['actionable-tasks'] }}</tasks>\n\n\nPlease answer the human query:\n<human>{{ $json.query }}</human>\n\n# Expected output:\nRespond directly to the output. Do not mention 'Based on the provided summary'.\n\nExample:\nUser: Can you summarize this videl: http://xxx\nAnswer: The video is discussing ...\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [880, 2800], "id": "005fe4e5-e1b2-46b3-a017-b1db3c47e4d0", "name": "AI Agent1"}, {"parameters": {"content": "# Output", "height": 383, "width": 1010, "color": 3}, "id": "f27379eb-ae8a-4cd1-8a04-0654b07b9515", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2520, 2200]}, {"parameters": {"content": "# Search\n", "height": 543, "width": 1470, "color": 7}, "id": "f0e1226f-edf9-479d-a08f-1cfa02702cb6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, 3380]}, {"parameters": {"promptType": "define", "text": "=Use your tool to search for relevant videos and respond to the human.a\nThe search query is: {{ $json.output.search_query }} \n\nAfter the reponse, return the 'video_id' properties from the metadata in a list, prefixed by the Youtube URL.\n\n# Example:\n\nUser: What video is talking about deepseek?\nAnswer: \nThe videos mention deepseek and discuss...\n\n<video_ids>\n<video_id>https://www.youtube.com/watch?v=xyz123</video_id>\n<video_id>https://www.youtube.com/watch?v=12345</video_id>\n</video_ids>", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [200, 3540], "id": "1e804966-aaf2-424c-bf9a-fa5a67497f13", "name": "Search Agent"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "video_transcript_query", "toolDescription": "Search for relevant videos from the vector store  using a search query.", "tableName": {"__rl": true, "value": "youtube_agent_data", "mode": "list", "cachedResultName": "youtube_agent_data"}, "topK": 6, "options": {"queryName": "match_youtube_data"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [420, 3800], "id": "e8a4ab94-ef27-41cc-8109-ab4137123443", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"operation": "get", "tableId": "videos", "filters": {"conditions": [{"keyName": "video_id", "keyValue": "={{ $fromAI('videoId') }}"}]}}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [260, 3780], "id": "********-873a-4374-9169-7841c2cc8448", "name": "get_video_by_videoId", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.session_id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-480, 3180], "id": "f017e258-c6e2-43cb-bbb9-1464f46ea176", "name": "Window Buffer Memory"}, {"parameters": {"url": "={{ $json.transcriptUrl }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 160], "id": "e986f487-9eaf-4b78-ba7a-214894c33091", "name": "Fetch transcript"}, {"parameters": {"url": "=https://www.youtube.com/watch?v={{ $json.video_id }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [120, 180], "id": "e2b685da-90bd-44c9-91bb-475148b9604c", "name": "Get video page"}, {"parameters": {"options": {"normalize": false}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [660, 160], "id": "38a5ed21-cf23-44fc-bc97-624965eb917c", "name": "XML"}, {"parameters": {"assignments": {"assignments": [{"id": "69d020a0-5116-4e63-809e-8c5cb2bb2b09", "name": "video_id", "value": "={{ $('Code').item.json.videoId }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 1240], "id": "7ef4d380-e4d3-4008-a31b-8c7b5887e040", "name": "Get Video ID"}, {"parameters": {"assignments": {"assignments": [{"id": "01781b3b-d819-4abf-8a46-626454d34dae", "name": "title", "value": "={{ $json.items[0].snippet.title }}", "type": "string"}, {"id": "f5207da6-ed58-4e90-bb6f-077b90908efa", "name": "description", "value": "={{ $json.items[0].snippet.description }}", "type": "string"}, {"id": "fd168156-4a06-411d-8a3f-5b7938d865eb", "name": "channelName", "value": "={{ $json.items[0].snippet.channelTitle }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [820, 1240], "id": "8a53636d-cdbe-4144-b6a4-b02208be215e", "name": "Extract video details"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1040, 1240], "id": "ba67c032-6980-4a23-b638-c81087c24676", "name": "Merge1"}, {"parameters": {"content": "# Get Transcript and video info", "height": 483, "width": 1170, "color": 7}, "id": "fefd80a1-56c6-431c-9b4c-bc06d36a3961", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 940]}, {"parameters": {"assignments": {"assignments": [{"id": "1c200573-d261-438a-98c2-7a0624c4df98", "name": "=data", "value": "=The video '{{ $('Extract video details').first().json.title }}' by {{ $('Extract video details').first().json.channelName }} was added successfully!`", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2260, 1660], "id": "c731ca1e-7a1d-4767-bd8d-2309990870bd", "name": "Generate output message", "executeOnce": true}, {"parameters": {"jsCode": "return { transcript: $input.first().json.transcript.text.map(item => item._).join(' ')}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [920, 180], "id": "26cb23f9-586b-476b-a617-8ebbc2b15bc1", "name": "Join transcript in code"}, {"parameters": {"assignments": {"assignments": [{"id": "87f25074-de10-4584-9ac3-417409298c3e", "name": "data", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1280, 2800], "id": "b87efc40-e6fd-41f3-b974-77060c9caa9d", "name": "Gather output data"}, {"parameters": {"assignments": {"assignments": [{"id": "3f0d1df2-cad9-4777-aa40-06215e4580ec", "name": "data", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [740, 3520], "id": "f580f22d-ab14-4891-968d-028631ed67e0", "name": "Prepare output"}, {"parameters": {"jsCode": "//console.log($input.first().json.data)\nconst html = $input.first().json.data;\n\nconst splittedHTML = html.split('\"captions\":');\nif (splittedHTML.length <= 1) {\n  if (html.includes('class=\"g-recaptcha\"')) {\n    return { success: false, error: \"Too Many Requests\" }\n  }\n  if (!html.includes('\"playabilityStatus\":')) {\n    return { success: false, error: \"Transcript unavailable 1\" }\n  }\n  return { success: false, error: \"Transcript disabled \" }\n}\n\nconst captions = (() => {\n  try {\n    return JSON.parse(\n      splittedHTML[1].split(',\"videoDetails')[0].replace('\\n', '')\n    );\n  } catch (e) {\n    return undefined;\n  }\n})()?.['playerCaptionsTracklistRenderer'];\n\nif (!captions) {\n  return { success: false, error: \"Transcript disabled \" }\n}\n\nif (!('captionTracks' in captions)) {\n  return { success: false, error: \"Transcript not available 2\" }\n}\n\nconst transcriptUrl = captions.captionTracks[0].baseUrl\n\nreturn { success: true, transcriptUrl, video_id: $ }\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, 160], "id": "f6eddd1e-c358-44b6-9d21-007682d7b2ce", "name": "Extract transcript url"}, {"parameters": {"url": "=https://www.googleapis.com/youtube/v3/videos?part=snippet&id={{ $('Get Video ID').item.json.video_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 1260], "id": "2a97bc2f-beba-4e2f-8010-c45d3701f0f7", "name": "Fetch Video Details", "credentials": {"youTubeOAuth2Api": {"id": "zqTrwRG8dQ1p3VEE", "name": "YouTube account"}}}, {"parameters": {"url": "=https://api.supadata.ai/v1/youtube/transcript?text=true&videoId={{ $json.video_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "Your Supadata API key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [300, 1140], "id": "ec962a07-f3fa-4046-aaeb-1161a5bbdd96", "name": "Get transcript from SUPADATA"}, {"parameters": {"jsCode": "return { transcript: $input.first().json.content}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 1140], "id": "bc766edf-6539-4829-a90d-9e18b529b1c2", "name": "Join transcript in code1"}, {"parameters": {"content": "# Alternative video transcript getter\n\nThis part can slot into 'Get transcript from SUPADATA' to 'Join transcript in code' by using the YT watch ui itself.\nBut if you do this too much, you might get forced to log in to proof you're not a bot. \n", "height": 483, "width": 1170, "color": 7}, "id": "4307c5d7-de5a-4e47-b7d4-b0bdea26d136", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Add User Message to DB": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Tag generator agent", "type": "ai_languageModel", "index": 0}, {"node": "Summarize agent", "type": "ai_languageModel", "index": 0}, {"node": "AI Agent1", "type": "ai_languageModel", "index": 0}, {"node": "Search Agent", "type": "ai_languageModel", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Switch": {"main": [[{"node": "Code", "type": "main", "index": 0}], [{"node": "Return text2", "type": "main", "index": 0}], [{"node": "Code1", "type": "main", "index": 0}], [{"node": "Search Agent", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Check if video exists", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Tag generator agent", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Summarize agent", "type": "ai_outputParser", "index": 0}]]}, "Tag generator agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Summarize agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Add video to supabase", "type": "main", "index": 0}]]}, "Check if video exists": {"main": [[{"node": "If row exists", "type": "main", "index": 0}]]}, "If row exists": {"main": [[{"node": "Return text", "type": "main", "index": 0}], [{"node": "Get Video ID", "type": "main", "index": 0}]]}, "Return text": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Add video to supabase": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Insert documents", "type": "ai_embedding", "index": 0}, {"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Insert documents", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert documents", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Insert documents": {"main": [[{"node": "Generate output message", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Return text2": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "Gather fields", "type": "main", "index": 0}]]}, "Gather fields": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Gather output data", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "Search Agent", "type": "ai_tool", "index": 0}]]}, "Search Agent": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "get_video_by_videoId": {"ai_tool": [[{"node": "Search Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}, {"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Get video page": {"main": [[{"node": "Extract transcript url", "type": "main", "index": 0}]]}, "Fetch transcript": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Join transcript in code", "type": "main", "index": 0}]]}, "Get Video ID": {"main": [[{"node": "Fetch Video Details", "type": "main", "index": 0}, {"node": "Get transcript from SUPADATA", "type": "main", "index": 0}]]}, "Extract video details": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Merge1": {"main": [[{"node": "Summarize agent", "type": "main", "index": 0}, {"node": "Tag generator agent", "type": "main", "index": 0}]]}, "Generate output message": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Gather output data": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Prepare output": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Extract transcript url": {"main": [[{"node": "Fetch transcript", "type": "main", "index": 0}]]}, "Fetch Video Details": {"main": [[{"node": "Extract video details", "type": "main", "index": 0}]]}, "Get transcript from SUPADATA": {"main": [[{"node": "Join transcript in code1", "type": "main", "index": 0}]]}, "Join transcript in code1": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "901bdc0a-bc67-46bc-b805-0e708d3be938", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "V4svhdBC0oiQmCND", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}