{"name": "Content Creator", "nodes": [{"parameters": {"options": {}}, "id": "3bbcaf26-a216-42c4-b4f7-81f678d84b7b", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [380, 900], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "id": "3dec7079-deaf-43b7-8243-e81a18a62754", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [880, 880], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "id": "f28016bf-c55f-4582-b8a2-d5409a4bac4b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-140, 880], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Basic LLM Chain').item.json.output.web_search_query }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "e2b93a93-2b59-458e-b5f4-d276ffe61627", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [100, 400], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "d5bee14d-7263-449d-9735-b446d1b9963c", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1520, 660]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "85edc7ab-c5da-4fea-90e7-13b1a3693788", "name": "Add User Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-780, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `Searching the web with query: ${$('Basic LLM Chain').item.json.output.web_search_query.replaceAll(\"+\", \" \")}`,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "589a7475-eed0-42dd-935d-02a148896713", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-140, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "********-2ff5-450e-a4bd-5f16379b981e", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1000, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "1428304f-c80b-4bec-a92a-7fce52797cf8", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1260, 660]}, {"parameters": {"options": {}}, "id": "583f66cf-1af0-41ad-bd89-e6f255c7e3cf", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-600, 580], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonSchemaExample": "{\n\t\"target_audience\": \"Software Engineers\",\n\t\"web_search_query\": \"Best+tech+stack for+local+AI+development.\"\n}"}, "id": "4c306db0-26aa-401c-a048-1267430cb2a3", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-440, 580]}, {"parameters": {"promptType": "define", "text": "=You are an expert at defining a target audience and web search query based on a user's description of what they want to create X, LinkedIn, and blog content for.\n\nThe user said:\n\n{{ $('Prep Input Fields').item.json.query }}\n\nCreate a target audience and web search query based on what the user said. The web search query needs to have pluses instead of spaces since it will be used as the \"q\" parameter to the web search GET request.", "hasOutputParser": true}, "id": "82776a0d-ac3e-4a4d-857e-ccac5e3d7316", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-560, 400]}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $('HTTP Request').item.json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "303c2147-3aa5-4ca0-89eb-937115d79864", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 400], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"Summarizing web research...\",\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "3aefd4e4-f0da-4f3d-b023-c0182e475a62", "name": "Add AI Message to DB1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [360, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=The user said:\n\n{{ $('Prep Input Fields').item.json.query }}\n\nWeb Research:\n{{ $('HTTP Request1').item.json.summary.toJsonString() }}\n\nTarget Audience:\n\n{{ $('Basic LLM Chain').item.json.output.target_audience }}", "options": {"systemMessage": "=# System Role  \nYou are an expert Twitter content creator specializing in transforming web research and a user query into engaging, concise tweets tailored to a specific target audience.  \n\n# Task Specification  \nUsing the provided web research, craft a tweet that is:  \n1. Short, concise, and optimized for Twitter’s character limit (280 characters).  \n2. Tailored to resonate with the target audience’s interests, needs, and goals.  \n3. Incorporates 1-2 emojis to enhance personality and appeal.  \n4. Offers value or insight and includes a clear call to action.  \n5. Contains 1-3 relevant hashtags.  \n6. Outputs only the tweet text—nothing else.  \n\n# Specifics and Context  \nThe tweet should distill the essence of the research into a single impactful message. It must grab attention, provide immediate value, and encourage engagement (e.g., likes, replies, or clicks).  \n\n# Examples  \n## Example 1  \n**Input:** Research about productivity tips for managers.  \n**Output:**  \n🔥 Overwhelmed by meetings and to-dos? Managers, here’s how to stay ahead:  \n- Use the Eisenhower Matrix.  \n- Block focus time.  \n- Delegate smarter.  \n\nWhat’s your top time-management hack? Let’s share! ⏰  \n\n#Leadership #Productivity  \n\n# Reminders  \n- Keep the tone approachable and engaging.  \n- Use emojis sparingly for emphasis.  \n- Ensure the tweet stays within 280 characters and is tailored to the audience.  \n- Only output the tweet text.  \n"}}, "id": "573828bf-d0c2-454c-9584-4511d7f16614", "name": "X", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [280, 660]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=The user said:\n\n{{ $('Prep Input Fields').item.json.query }}\n\nWeb Research:\n{{ $('HTTP Request1').item.json.summary.toJsonString() }}\n\nTarget Audience:\n\n{{ $('Basic LLM Chain').item.json.output.target_audience }}", "options": {"systemMessage": "# System Role\nYou are a skilled and creative blog writer, capable of crafting engaging, concise, and well-structured two-paragraph blog articles based on provided content.\n\n# Task Specification\nWrite a two-paragraph blog article using the provided content. The blog should be coherent, engaging, and informative, tailored to a general audience. Ensure the tone is professional yet approachable, and the structure flows logically from introduction to conclusion.\n\n# Specifics and Context\nThis task is essential for producing quick, high-quality blog articles that capture readers' attention while accurately conveying the intended message. By writing clear and engaging content, you help brands or individuals establish thought leadership and connect with their audience effectively.\n\n# Examples\n## Example 1\n**Input:**  \nContent: \"Remote work has grown 44% in the last five years. Benefits include flexibility and reduced commute times. Challenges include maintaining productivity and combating isolation.\"\n\n**Output:**  \nRemote work has become a transformative trend, with a 44% increase in adoption over the past five years. The appeal lies in its flexibility, allowing employees to tailor their schedules and eliminate time-consuming commutes. This shift has unlocked new possibilities for work-life balance and broadened the talent pool for businesses willing to embrace remote setups.\n\nHowever, remote work isn’t without its challenges. Employees often face difficulties in maintaining productivity outside a structured office environment and struggle with feelings of isolation. Addressing these concerns requires thoughtful solutions, such as virtual collaboration tools and strategies to foster connection, ensuring remote work remains both productive and fulfilling.\n\n## Example 2\n**Input:**  \nContent: \"The Mediterranean diet includes fruits, vegetables, whole grains, and healthy fats like olive oil. Studies show it reduces the risk of heart disease and supports brain health.\"\n\n**Output:**  \nThe Mediterranean diet has long been celebrated as one of the healthiest eating patterns in the world. Emphasizing fresh fruits, vegetables, whole grains, and heart-healthy fats like olive oil, this diet is as delicious as it is nutritious. Its flavorful diversity makes it easy to adopt and sustain, whether you’re enjoying a vibrant Greek salad or a wholesome bowl of minestrone.\n\nWhat sets the Mediterranean diet apart is its scientifically backed health benefits. Numerous studies highlight its ability to reduce the risk of heart disease and support cognitive health, making it a cornerstone for longevity and wellness. By prioritizing natural, unprocessed foods, this lifestyle offers a sustainable approach to eating well and living better.\n\n# Reminders\n- Maintain clarity and logical flow between paragraphs.\n- Ensure the tone is engaging yet professional.\n- Keep the blog concise and aligned with the provided content.\n"}}, "id": "1320c02a-4119-4921-a9ca-40e827b8aaf5", "name": "Blog Writer", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [760, 660]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `## LinkedIn Post:\\n\\n${$('LinkedIn').item.json.output}`,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "63a9035e-c54e-4eee-bd34-fa85b1d9834f", "name": "Add AI Message to DB2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [120, 840], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `## X Post:\\n\\n${$('X').item.json.output}`,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "79aff67e-ea4f-4434-811a-dce4425f3313", "name": "Add AI Message to DB3", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [600, 840], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `## Blog Post:\\n\\n${$('Blog Writer').item.json.output}`,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "4477ee61-1baf-4df8-9a68-d125bd761779", "name": "Add AI Message to DB4", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1080, 840], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"httpMethod": "POST", "path": "invoke-content-creator", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "e237f98f-7d92-4c8c-bf09-65b4d56f5422", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1240, 400], "webhookId": "9a6c4630-b422-4d42-b894-81ecfe881ffe", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=The user said:\n\n{{ $('Prep Input Fields').item.json.query }}\n\nWeb Research:\n{{ $('HTTP Request1').item.json.summary.toJsonString() }}\n\nTarget Audience:\n\n{{ $('Basic LLM Chain').item.json.output.target_audience }}", "options": {"systemMessage": "# System Role  \nYou are an expert LinkedIn content creator specializing in transforming incoming web research and a user query into highly engaging posts tailored to a specific target audience.  \n\n# Task Specification  \nUsing the provided web research, craft a LinkedIn post that is:  \n1. Written in a concise, engaging tone optimized for readability on mobile.  \n2. Tailored specifically to the target audience’s interests, needs, and professional goals.  \n3. Plain text only, with frequent line breaks for clarity.  \n4. Incorporates 1-2 emojis to enhance personality and appeal.  \n5. Provides actionable value and includes a clear call to action.  \n6. Contains 3-5 relevant hashtags.  \n7. Outputs only the post text—nothing else.  \n\n# Specifics and Context  \nThe post should succinctly capture the core message of the article while resonating with the audience’s values. It must sound human and conversational, staying under 3,000 characters.  \n\n# Examples  \n## Example 1  \n**Input:** Research about productivity tips for managers.  \n**Output:**  \n🔥 Time to Supercharge Your Productivity!  \n\nManagers, are your days packed with back-to-back meetings and endless to-do lists? Here’s the secret to working smarter, not harder: [insight summary].  \n\n👉 Top tips to stay ahead:  \n1. Prioritize tasks using the Eisenhower Matrix.  \n2. Block time on your calendar for deep focus.  \n3. Delegate effectively to your team.  \n\nWhat strategies help you lead and manage your time effectively? Share your thoughts below!  \n\n#Leadership #TimeManagement #Productivity  \n\n# Reminders  \n- Ensure the content aligns with the target audience's interests and challenges.  \n- Always include 1-2 emojis and a call to action.  \n- Use plain text and only output the post content.  \n"}}, "id": "9ac2245b-7f74-4275-a449-d351c7a4a6db", "name": "LinkedIn", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-200, 660]}, {"parameters": {"content": "# Content Creator Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@nateherk)\n\nThis n8n-powered agent is your personal content creation assistant that generates engaging, research-backed content for multiple social media platforms. It takes your target audience and topic as input, conducts web research, and creates tailored content for LinkedIn, X (formerly Twitter), and blog posts.\n\n## Features\n\n- Conducts web research on specified topics\n- Analyzes target audience preferences\n- Generates platform-specific content\n- Creates cohesive multi-platform content strategies\n- Ensures content aligns with each platform's best practices", "height": 422.5916179238002, "width": 651.0139534883727, "color": 6}, "id": "785cdf46-4297-41a8-a6ef-13d693be017d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1400, 620]}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.chayahfitness.com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer 7vnvV*ELn.#FWNV(87g2WQc", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.chayahfitness.com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "Supabase", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.chayahfitness.com/webhook-test/invoke-n8n-expert", "executionMode": "test"}}]}, "connections": {"OpenAI Chat Model1": {"ai_languageModel": [[{"node": "X", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Blog Writer", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "LinkedIn", "type": "ai_languageModel", "index": 0}]]}, "Add User Message to DB": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Add AI Message to DB1", "type": "main", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Add AI Message to DB1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "LinkedIn", "type": "main", "index": 0}]]}, "X": {"main": [[{"node": "Add AI Message to DB3", "type": "main", "index": 0}]]}, "Blog Writer": {"main": [[{"node": "Add AI Message to DB4", "type": "main", "index": 0}]]}, "Add AI Message to DB2": {"main": [[{"node": "X", "type": "main", "index": 0}]]}, "Add AI Message to DB3": {"main": [[{"node": "Blog Writer", "type": "main", "index": 0}]]}, "Add AI Message to DB4": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "LinkedIn": {"main": [[{"node": "Add AI Message to DB2", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8b683d72-a944-4967-86c7-7393865e28cc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "TAs656hlt8SFEVs5", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}