{"name": "Agent <PERSON><PERSON> Agent", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "c92dfb4d-571a-4e08-ba92-4216404ec455", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1700, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "e423ed64-4664-4e34-b7c2-d71148963a24", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "5881a308-ab65-4034-a449-9c1f0d90743e", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1460, 400]}, {"parameters": {"model": "claude-3-5-haiku-********", "options": {}}, "id": "66920cf0-b301-4d0e-80ec-204696fdbf76", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [980, 620], "credentials": {"anthropicApi": {"id": "AiDvkdxUFBeRQmnE", "name": "Anthropic account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields').item.json.session_id}}", "tableName": "messages", "contextWindowLength": 10}, "id": "1c06de3a-8fc4-4ccf-9dc7-b9f1f5bf692a", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1100, 620], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "d9fec84b-86f0-4230-9fd4-c1cb392ff8b5", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "d6540989-6349-47aa-8c2a-8dd93b262b6e", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [500, 400], "webhookId": "d9fec84b-86f0-4230-9fd4-c1cb392ff8b5", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "# Agent Node Sample Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nThis is a sample n8n workflow that demonstrates the minimal required components to build an agent for the oTTomator Live Agent Studio using the \"Agent\" node in n8n. The \"Agent\" node manages conversation history itself and is compatible with the Live Agent Studio.\n\n## Core Components\n\n1. **Webhook Endpoint**\n   - Accepts POST requests with authentication\n   - Processes incoming queries with user and session information\n   - Provides secure communication via header authentication\n\n2. **Input Processing**\n   - Extracts key fields from incoming requests:\n     - query: The user's question or command\n     - user_id: Unique identifier for the user\n     - request_id: Request tracking ID\n     - session_id: Current session identifier\n\n3. **Database Integration**\n   - Uses Supabase for message storage\n   - Records both user messages and AI responses\n   - Maintains conversation history with metadata\n\n4. **Response Handling**\n   - Structured response format for consistency\n   - Includes success/failure status\n   - Returns formatted responses via webhook", "height": 763.4375, "width": 589.875, "color": 6}, "id": "19bfc66c-0bfa-414c-b234-057b05873c75", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-160, 200]}, {"parameters": {"promptType": "define", "text": "={{$('Prep Input Fields').item.json.query}}", "options": {"systemMessage": "=Enter your system message here..."}}, "id": "24599191-6cd2-4b7b-aade-cf45dd1ccbb6", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1040, 400]}, {"parameters": {"content": "## Add your own agent tools and feel free to use any provider and LLM.", "height": 100.31395348837148, "width": 422.59302325581405, "color": 6}, "id": "0376123d-d772-4e74-a217-b1bac0e365fb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [940, 760]}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.[your n8n url].com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer YOUR BEARER TOKEN", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.[your n8n url].com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "Supabase", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.[your n8n url].com/webhook-test/invoke-agent", "executionMode": "test"}}]}, "connections": {"Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8e077fe3-9da1-496b-a2e5-18ae1676f71c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "a4lg63apQMOxsg5A", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}