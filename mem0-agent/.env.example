# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
OPENAI_API_KEY=

# Below variables are for V2 and beyond:

# The LLM to use (defaults to gpt-4o-mini)
MODEL_CHOICE=gpt-4o-mini

# Get your Supabase DATABASE_URL from the Database section of your Supabase project settings-
# https://supabase.com/dashboard/project/<your project ID>/settings/database
# Make sure you replace the [YOUR-PASSWORD] placeholder with your DB password you set when creating your account.
# Be sure ot use URL coding for your password (example, '@' is %40, '?' is %3F, etc.)
# You can reset this if needed on the same database settings page.
DATABASE_URL=

# Supabase configuration for authentication
# Get these from your Supabase project settings -> API
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key