# Get your OpenRouter API Key from https://openrouter.ai/keys
OPENROUTER_API_KEY="sk-or-"

OPENROUTER_MODEL="mistralai/mistral-7b-instruct"
OPENROUTER_VLM_MODEL="meta-llama/llama-3.2-11b-vision-instruct:free"

# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL='https://yaiznnodpdb.supabase.co'

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY="e"

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN='toto'