# Get your SUPABASE URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN=

# Get your Open Router API Key here after registering:
# https://openrouter.ai/settings/keys
OPEN_ROUTER_API_KEY=

# The LLM you want to use from OpenRouter. See the list of models here:
# https://openrouter.ai/models
# Example: deepseek/deepseek-chat 
LLM_MODEL=

# This this personal Hunter.io access token by following these instructions -
HUNTER_API_KEY=